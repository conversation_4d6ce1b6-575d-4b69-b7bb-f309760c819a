{"model_params": {"input_size": 7, "hidden_size": 128, "num_layers": 3, "output_size": 1, "dropout": 0.1, "bidirectional": true, "use_attention": true, "attention_heads": 8, "transformer_layers": 2, "d_model": 128, "d_ff": 512}, "training_params": {"num_epochs": 100, "batch_size": 32, "learning_rate": 0.001, "weight_decay": 1e-05, "early_stopping_patience": 15, "lr_scheduler": "StepLR", "lr_step_size": 30, "lr_gamma": 0.5, "gradient_clip": 1.0, "optimizer": "<PERSON>", "loss_function": "MSE", "validation_split": 0.2, "shuffle": true, "num_workers": 4, "pin_memory": true}, "data_params": {"sequence_length": 24, "prediction_horizon": 1, "train_ratio": 0.7, "val_ratio": 0.15, "test_ratio": 0.15, "normalize": true, "normalization_method": "MinMaxScaler", "feature_columns": ["A相电压_均值(V)", "A相电流_均值(A)", "降水量(mm)", "平均气温(℃)", "最低气温(℃)", "最高气温(℃)", "总有功功率_总和(kW)"], "target_column": "总有功功率_总和(kW)", "time_column": "充电时间", "missing_value_strategy": "interpolate", "outlier_detection": true, "outlier_threshold": 3.0}, "ssa_params": {"population_size": 30, "max_iterations": 50, "dimension": 10, "lower_bound": [0.0001, 16, 1, 0.0, 0.1, 1, 64, 256, 1, 2], "upper_bound": [0.01, 256, 5, 0.5, 1.0, 10, 512, 1024, 8, 6], "parameter_names": ["learning_rate", "batch_size", "num_layers", "dropout", "weight_decay", "sequence_length", "hidden_size", "d_model", "attention_heads", "transformer_layers"], "fitness_function": "validation_loss", "selection_pressure": 2.0, "crossover_rate": 0.8, "mutation_rate": 0.1, "elite_size": 5, "convergence_threshold": 1e-06, "max_stagnation": 10}, "vmd_params": {"alpha": 2000.0, "tau": 0.0, "K": 5, "DC": false, "init": 1, "tol": 1e-07, "max_iter": 500, "apply_vmd": true, "vmd_features": ["总有功功率_总和(kW)", "A相电流_均值(A)"], "reconstruction_method": "sum", "mode_selection": "all"}, "visualization_params": {"figure_size": [12, 8], "dpi": 100, "style": "seaborn-v0_8", "color_palette": "viridis", "font_family": "SimHei", "font_size": 12, "title_size": 14, "label_size": 10, "legend_size": 9, "grid": true, "grid_alpha": 0.3, "line_width": 2, "marker_size": 6, "alpha": 0.8, "save_format": "png", "save_dpi": 300, "tight_layout": true, "show_plots": true, "plot_types": {"training_history": true, "prediction_comparison": true, "feature_importance": true, "correlation_matrix": true, "data_distribution": true, "residual_analysis": true, "time_series": true, "performance_metrics": true}}, "system_params": {"device": "auto", "cuda_device": 0, "random_seed": 42, "deterministic": true, "benchmark": false, "num_threads": 4, "memory_limit": "8GB", "cache_size": "1GB", "log_level": "INFO", "log_file": "logs/training.log", "checkpoint_dir": "checkpoints", "model_save_dir": "models", "results_save_dir": "results", "data_cache_dir": "cache", "temp_dir": "temp", "backup_enabled": true, "backup_interval": 10, "auto_save": true, "save_interval": 5, "max_checkpoints": 5, "cleanup_temp": true}, "gui_params": {"window_title": "电动汽车充电负荷预测系统", "window_size": [1400, 900], "window_resizable": true, "window_center": true, "theme": "light", "font_family": "Microsoft YaHei", "font_size": 9, "icon_path": "assets/icon.ico", "splash_screen": true, "splash_duration": 3000, "status_bar": true, "toolbar": true, "menu_bar": true, "tab_position": "top", "show_tooltips": true, "auto_refresh": true, "refresh_interval": 1000, "progress_update_interval": 100, "max_log_lines": 1000, "auto_scroll_logs": true, "confirm_exit": true, "save_window_state": true, "restore_window_state": true, "recent_files_limit": 10, "default_file_types": {"data": [".csv", ".xlsx", ".xls"], "model": [".pth", ".pkl"], "config": [".json", ".yaml", ".yml"], "export": [".csv", ".xlsx", ".png", ".pdf"]}}, "performance_params": {"enable_profiling": false, "profile_output": "profiles", "memory_monitoring": true, "gpu_monitoring": true, "cpu_monitoring": true, "disk_monitoring": false, "network_monitoring": false, "performance_log": "logs/performance.log", "alert_thresholds": {"cpu_usage": 90.0, "memory_usage": 85.0, "gpu_usage": 95.0, "gpu_memory": 90.0, "disk_usage": 95.0, "training_time": 3600}, "optimization": {"mixed_precision": false, "gradient_accumulation": 1, "dataloader_workers": 4, "pin_memory": true, "non_blocking": true, "persistent_workers": true, "prefetch_factor": 2}}, "export_params": {"default_format": "csv", "include_metadata": true, "include_config": true, "include_metrics": true, "timestamp_format": "%Y%m%d_%H%M%S", "compression": false, "compression_format": "zip", "export_plots": true, "plot_formats": ["png", "pdf"], "report_template": "templates/report_template.html", "generate_report": true, "report_format": "html", "include_code": false, "include_logs": false}, "validation_params": {"cross_validation": false, "cv_folds": 5, "cv_strategy": "TimeSeriesSplit", "validation_metrics": ["MSE", "RMSE", "MAE", "MAPE", "R2", "explained_variance"], "test_size": 0.2, "validation_frequency": 1, "early_stopping_metric": "val_loss", "early_stopping_mode": "min", "metric_averaging": "macro", "confidence_interval": 0.95, "bootstrap_samples": 1000, "statistical_tests": true}}