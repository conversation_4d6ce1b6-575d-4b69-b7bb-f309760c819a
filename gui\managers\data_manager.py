"""
数据管理器 - 负责数据加载、预处理和管理
Data Manager - Handles data loading, preprocessing and management
"""

import pandas as pd
import numpy as np
import os
import sys
from typing import Optional, Dict, Any, Tuple
import threading
import queue

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ev_charging_prediction import DataPreprocessor


class DataManager:
    """数据管理器类"""
    
    def __init__(self):
        self.preprocessor = DataPreprocessor()
        self.raw_data = None
        self.processed_data = None
        self.data_info = {}
        self.data_quality_report = {}
        self._lock = threading.Lock()
        
    def load_data(self, file_path: str, progress_callback=None) -> bool:
        """
        加载数据文件
        
        Args:
            file_path: 数据文件路径
            progress_callback: 进度回调函数
            
        Returns:
            bool: 加载是否成功
        """
        try:
            with self._lock:
                if progress_callback:
                    progress_callback(10, "正在读取数据文件...")
                
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"文件不存在: {file_path}")
                
                # 读取数据
                self.raw_data = pd.read_csv(file_path)
                
                if progress_callback:
                    progress_callback(30, "正在分析数据结构...")
                
                # 生成数据信息
                self._generate_data_info()
                
                if progress_callback:
                    progress_callback(50, "正在进行数据质量检查...")
                
                # 生成数据质量报告
                self._generate_quality_report()
                
                if progress_callback:
                    progress_callback(100, "数据加载完成")
                
                return True
                
        except Exception as e:
            error_msg = f"数据加载失败: {str(e)}"
            print(error_msg)
            # 如果有进度回调，尝试通知错误
            if progress_callback:
                try:
                    # 检查是否是线程安全问题
                    if "main thread is not in main loop" in str(e):
                        print("检测到线程安全问题，跳过进度回调")
                    else:
                        progress_callback(0, error_msg)
                except Exception as callback_error:
                    print(f"进度回调失败: {callback_error}")
                    pass  # 忽略回调错误
            return False
    
    def preprocess_data(self, config: Dict[str, Any], progress_callback=None) -> bool:
        """
        预处理数据
        
        Args:
            config: 预处理配置
            progress_callback: 进度回调函数
            
        Returns:
            bool: 预处理是否成功
        """
        try:
            with self._lock:
                if self.raw_data is None:
                    raise ValueError("请先加载数据")
                
                if progress_callback:
                    progress_callback(10, "开始数据预处理...")
                
                # 数据清洗和预处理
                df = self.preprocessor.load_and_clean_data_from_df(self.raw_data.copy())
                
                if progress_callback:
                    progress_callback(30, "创建时间特征...")
                
                # 创建时间特征
                df = self.preprocessor.create_time_features(df)
                
                if progress_callback:
                    progress_callback(60, "准备序列数据...")
                
                # 准备序列数据
                sequence_length = config.get('sequence_length', 24)
                X, y, idx_arr = self.preprocessor.prepare_sequences(df, sequence_length)
                
                if progress_callback:
                    progress_callback(80, "分割数据集...")
                
                # 分割数据
                train_data, val_data, test_data = self.preprocessor.split_data(X, y, idx_arr)
                
                self.processed_data = {
                    'train': train_data,
                    'val': val_data,
                    'test': test_data,
                    'feature_columns': self.preprocessor.feature_columns,
                    'scaler': self.preprocessor.scaler,
                    'load_scaler': self.preprocessor.load_scaler
                }
                
                if progress_callback:
                    progress_callback(100, "数据预处理完成")
                
                return True
                
        except Exception as e:
            error_msg = f"数据预处理失败: {str(e)}"
            print(error_msg)
            # 如果有进度回调，尝试通知错误
            if progress_callback:
                try:
                    # 检查是否是线程安全问题
                    if "main thread is not in main loop" in str(e):
                        print("检测到线程安全问题，跳过进度回调")
                    else:
                        progress_callback(0, error_msg)
                except Exception as callback_error:
                    print(f"进度回调失败: {callback_error}")
                    pass  # 忽略回调错误
            return False
    
    def _generate_data_info(self):
        """生成数据基本信息"""
        if self.raw_data is None:
            return
            
        self.data_info = {
            'shape': self.raw_data.shape,
            'columns': list(self.raw_data.columns),
            'dtypes': self.raw_data.dtypes.to_dict(),
            'memory_usage': self.raw_data.memory_usage(deep=True).sum(),
            'null_counts': self.raw_data.isnull().sum().to_dict(),
            'numeric_columns': list(self.raw_data.select_dtypes(include=[np.number]).columns),
            'categorical_columns': list(self.raw_data.select_dtypes(include=['object']).columns)
        }
    
    def _generate_quality_report(self):
        """生成数据质量报告"""
        if self.raw_data is None:
            return
            
        df = self.raw_data
        
        # 基本统计信息
        numeric_stats = df.describe() if len(df.select_dtypes(include=[np.number]).columns) > 0 else pd.DataFrame()
        
        # 缺失值分析
        missing_analysis = {
            'total_missing': df.isnull().sum().sum(),
            'missing_percentage': (df.isnull().sum() / len(df) * 100).to_dict(),
            'columns_with_missing': df.columns[df.isnull().any()].tolist()
        }
        
        # 重复值分析
        duplicate_analysis = {
            'total_duplicates': df.duplicated().sum(),
            'duplicate_percentage': df.duplicated().sum() / len(df) * 100
        }
        
        # 异常值分析（仅对数值列）
        outlier_analysis = {}
        for col in df.select_dtypes(include=[np.number]).columns:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
            outlier_analysis[col] = {
                'count': len(outliers),
                'percentage': len(outliers) / len(df) * 100,
                'lower_bound': lower_bound,
                'upper_bound': upper_bound
            }
        
        self.data_quality_report = {
            'basic_stats': numeric_stats.to_dict() if not numeric_stats.empty else {},
            'missing_analysis': missing_analysis,
            'duplicate_analysis': duplicate_analysis,
            'outlier_analysis': outlier_analysis,
            'data_types_distribution': df.dtypes.value_counts().to_dict()
        }
    
    def get_data(self) -> Optional[pd.DataFrame]:
        """获取完整的原始数据"""
        return self.raw_data

    def get_data_preview(self, n_rows: int = 100) -> Optional[pd.DataFrame]:
        """获取数据预览"""
        if self.raw_data is None:
            return None
        return self.raw_data.head(n_rows)
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息"""
        return self.data_info
    
    def get_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        return self.data_quality_report
    
    def get_processed_data(self) -> Optional[Dict[str, Any]]:
        """获取预处理后的数据"""
        return self.processed_data
    
    def has_data(self) -> bool:
        """检查是否有数据"""
        return self.raw_data is not None and not self.raw_data.empty

    def is_data_valid(self) -> bool:
        """检查数据是否有效"""
        if self.raw_data is None:
            return False

        try:
            # 检查数据是否为空
            if self.raw_data.empty:
                return False

            # 检查是否有有效的列
            if len(self.raw_data.columns) == 0:
                return False

            # 检查是否有数值列
            numeric_cols = self.raw_data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                print("警告: 数据中没有数值列")
                return False

            return True

        except Exception as e:
            print(f"数据验证失败: {e}")
            return False
    
    def has_processed_data(self) -> bool:
        """检查是否有预处理后的数据"""
        return self.processed_data is not None
    
    def get_column_statistics(self, column_name: str) -> Optional[Dict[str, Any]]:
        """获取指定列的统计信息"""
        if self.raw_data is None or column_name not in self.raw_data.columns:
            return None
            
        col_data = self.raw_data[column_name]
        
        if col_data.dtype in [np.number]:
            return {
                'type': 'numeric',
                'count': col_data.count(),
                'mean': col_data.mean(),
                'std': col_data.std(),
                'min': col_data.min(),
                'max': col_data.max(),
                'median': col_data.median(),
                'null_count': col_data.isnull().sum(),
                'unique_count': col_data.nunique()
            }
        else:
            return {
                'type': 'categorical',
                'count': col_data.count(),
                'unique_count': col_data.nunique(),
                'null_count': col_data.isnull().sum(),
                'top_values': col_data.value_counts().head(10).to_dict()
            }
    
    def export_processed_data(self, file_path: str) -> bool:
        """导出预处理后的数据"""
        try:
            if self.processed_data is None:
                raise ValueError("没有预处理后的数据可导出")
            
            # 这里可以根据需要导出不同格式的数据
            # 暂时导出训练数据的基本信息
            info = {
                'train_shape': self.processed_data['train'][0].shape,
                'val_shape': self.processed_data['val'][0].shape,
                'test_shape': self.processed_data['test'][0].shape,
                'feature_columns': self.processed_data['feature_columns']
            }
            
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(info, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"导出数据失败: {str(e)}")
            return False
    
    def clear_data(self):
        """清空数据"""
        with self._lock:
            self.raw_data = None
            self.processed_data = None
            self.data_info = {}
            self.data_quality_report = {}
