timestamp,config,training_result,test_metrics,model_path
20250801_165637,"{'model_params': {'hidden_size': 128, 'num_layers': 2, 'dropout': 0.2, 'model_type': 'gru', 'loss_type': 'mse', 'alpha': 1.0}, 'training_params': {'num_epochs': 50, 'batch_size': 32, 'learning_rate': 0.001, 'weight_decay': 0.0001, 'patience': 10, 'validation_split': 0.1}, 'data_params': {'sequence_length': 24, 'train_ratio': 0.7, 'val_ratio': 0.1, 'test_ratio': 0.2, 'normalize_features': True, 'handle_missing': True, 'remove_outliers': True}, 'ssa_params': {'use_ssa_optimization': True, 'pop_size': 8, 'max_iter': 5, 'use_parallel': True, 'n_workers': 4}, 'vmd_params': {'use_vmd': True, 'alpha': 2000, 'tau': 0, 'K': 3, 'DC': 0, 'init': 1, 'tol': 1e-07}, 'visualization_params': {'figure_size': (12, 8), 'dpi': 100, 'style': 'seaborn', 'color_palette': 'viridis', 'show_grid': True, 'save_plots': True}, 'system_params': {'use_gpu': True, 'gpu_memory_fraction': 0.8, 'random_seed': 42, 'log_level': 'INFO', 'save_checkpoints': True, 'checkpoint_interval': 10}, 'gui_params': {'theme': 'default', 'font_size': 10, 'auto_refresh': True, 'refresh_interval': 1000, 'show_tooltips': True, 'remember_window_size': True}, 'hidden_size': 512, 'num_layers': 3, 'dropout': 0.15, 'learning_rate': 0.003673, 'batch_size': 32, 'loss_type': 'wmse', 'weight_decay': 0.000138, 'alpha': 1.98, 'model_type': 'gru'}","{'best_val_loss': 0.017489977979234288, 'total_epochs': 27, 'early_stopped': True}","{'mse': 0.0520946137003107, 'rmse': 0.22824244500160504, 'mae': 0.1784723392825534, 'r2': -1.0675197596173494}",results\20250801_165138_GUI_Session_165138\models\best_model_20250801_165138.pth
