import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import os
import sys
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .managers.data_manager import DataManager
from .managers.model_manager import ModelManager
from .managers.config_manager import ConfigManager
from .managers.visualization_manager import VisualizationManager
from .managers.results_manager import get_results_manager, start_training_session

from .components.parameter_panel import ParameterPanel
from .components.data_panel import DataPanel
from .components.training_panel import TrainingPanel
from .components.ev_training_panel import EVTrainingPanel
from .components.prediction_panel import PredictionPanel
from .components.metrics_panel import MetricsPanel
from .components.status_panel import StatusPanel
from .components.visualization_panel import VisualizationPanel

from .utils.gui_utils import center_window, setup_style, setup_all_fonts, get_font_manager
from .utils.log_manager import get_log_manager, setup_training_logging
from .utils.threading_utils import ThreadSafeQueue
from .utils.realtime_sync_manager import get_sync_manager, initialize_sync_manager


class EVChargingPredictionGUI:
    """电动汽车充电负荷预测系统主界面"""
    
    def __init__(self):
        self.root = tk.Tk()

        # 在创建根窗口后设置字体系统
        try:
            setup_all_fonts()
            self.font_manager = get_font_manager()
            print("✅ 字体系统初始化成功")
        except Exception as e:
            print(f"⚠️ 字体系统初始化失败: {e}")
            self.font_manager = None

        self.setup_main_window()
        
        # 初始化管理器
        self.data_manager = DataManager()
        self.model_manager = ModelManager()
        self.config_manager = ConfigManager()
        self.visualization_manager = VisualizationManager()

        # 初始化结果管理器并开始新的训练会话
        self.results_manager = get_results_manager()
        session_name = f"GUI_Session_{datetime.now().strftime('%H%M%S')}"
        self.current_session_dir = start_training_session(session_name)
        print(f"🎯 GUI训练会话已开始: {self.current_session_dir}")
        
        # 线程安全的消息队列
        self.message_queue = ThreadSafeQueue()
        
        # 创建界面组件
        self.create_menu_bar()
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
        # 设置样式
        setup_style()
        
        # 启动消息处理
        self.process_messages()
        
        # 应用状态
        self.is_training = False
        self.current_config = None
        self.gui_initialized = False
        self.last_update_time = time.time()
        
        # 启动实时更新定时器
        self.start_real_time_updates()

        # 延迟启动后台任务和完成初始化
        self.root.after(1000, self.complete_initialization)

    def start_real_time_updates(self):
        """启动实时更新系统"""
        # 启动高频状态更新
        self.update_real_time_status()
        
        # 启动日志同步检查
        self.sync_log_output()
        
    def update_real_time_status(self):
        """实时更新状态信息"""
        try:
            current_time = time.time()
            
            # 检查是否需要更新GUI组件状态
            if hasattr(self, 'status_panel') and self.gui_initialized:
                # 更新状态面板（如果存在）
                if hasattr(self.status_panel, 'update_app_status'):
                    self.status_panel.update_app_status()
            
            # 检查训练状态变化
            if hasattr(self, 'training_panel') and self.gui_initialized:
                if hasattr(self.training_panel, 'update_training_status'):
                    self.training_panel.update_training_status()
            
            self.last_update_time = current_time
            
        except Exception as e:
            print(f"实时状态更新错误: {e}")
        finally:
            # 每500ms更新一次状态
            self.root.after(500, self.update_real_time_status)
    
    def sync_log_output(self):
        """同步日志输出到GUI"""
        try:
            # 检查是否有新的日志需要同步
            log_manager = get_log_manager()
            
            # 确保日志回调已注册
            if hasattr(self, 'status_panel') and self.gui_initialized:
                if hasattr(self.status_panel, 'add_log'):
                    # 如果日志回调还没有注册，注册它
                    if not hasattr(self, '_log_callback_registered'):
                        log_manager.add_log_callback(self.status_panel.add_log)
                        self._log_callback_registered = True
                        print("✅ 日志回调已注册到状态面板")
        
        except Exception as e:
            print(f"日志同步错误: {e}")
        finally:
            # 每1秒检查一次日志同步
            self.root.after(1000, self.sync_log_output)

    def _register_gui_components_to_sync_manager(self):
        """注册GUI组件到实时同步管理器"""
        try:
            # 注册状态面板
            if hasattr(self, 'status_panel'):
                self.sync_manager.register_gui_component(
                    'status_panel',
                    self.status_panel,
                    ['check_app_status_real_time', 'update_gpu_info_real_time']
                )
            
            # 注册训练面板
            if hasattr(self, 'training_panel'):
                self.sync_manager.register_gui_component(
                    'training_panel',
                    self.training_panel,
                    ['update_training_status']
                )
            
            # 注册EV训练面板
            if hasattr(self, 'ev_training_panel'):
                self.sync_manager.register_gui_component(
                    'ev_training_panel',
                    self.ev_training_panel,
                    ['monitor_training_status', 'update_metrics_display', 'reset_training_history']
                )
            
            # 注册预测面板
            if hasattr(self, 'prediction_panel'):
                self.sync_manager.register_gui_component(
                    'prediction_panel',
                    self.prediction_panel,
                    []
                )
            
            # 注册可视化面板
            if hasattr(self, 'visualization_panel'):
                self.sync_manager.register_gui_component(
                    'visualization_panel',
                    self.visualization_panel,
                    []
                )
                
            print("✅ GUI组件已注册到同步管理器")
            
        except Exception as e:
            print(f"注册GUI组件到同步管理器失败: {e}")

    def complete_initialization(self):
        """完成GUI初始化"""
        try:
            # 标记GUI已初始化
            self.gui_initialized = True

            # 初始化实时同步管理器
            self.sync_manager = initialize_sync_manager(self)
            
            # 注册GUI组件到同步管理器
            gui_components_for_sync = {
                'ev_training_panel': 
                    ['monitor_training_status', 'update_metrics_display', 'reset_training_history']
            }
            self._register_gui_components_to_sync_manager()

            # 启动后台任务
            self.start_background_tasks()

            print("✅ GUI初始化完成，实时同步已启动")

        except Exception as e:
            print(f"完成初始化失败: {e}")

    def is_gui_ready(self) -> bool:
        """检查GUI是否已准备好"""
        return getattr(self, 'gui_initialized', False)

    def start_background_tasks(self):
        """启动后台任务"""
        try:
            # 启动GPU状态检查
            self.check_gpu_status()

            # 启动系统监控
            if hasattr(self, 'status_panel') and self.status_panel:
                self.status_panel.start_monitoring()
        except Exception as e:
            print(f"启动后台任务失败: {e}")

    def setup_main_window(self):
        """设置主窗口"""
        self.root.title("电动汽车充电负荷预测系统 v1.0")
        self.root.geometry("1400x900")
        center_window(self.root, 1400, 900)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
            
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建项目", command=self.new_project)
        file_menu.add_command(label="打开项目", command=self.open_project)
        file_menu.add_command(label="保存项目", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="导入数据", command=self.import_data)
        file_menu.add_command(label="导出结果", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="参数设置", command=self.show_parameter_panel)
        edit_menu.add_command(label="重置配置", command=self.reset_config)
        
        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="全屏", command=self.toggle_fullscreen)
        view_menu.add_command(label="刷新", command=self.refresh_all)
        
        # 训练菜单
        training_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="训练", menu=training_menu)
        training_menu.add_command(label="🚀 EV模型训练", command=self.show_ev_training_panel)
        training_menu.add_command(label="⚡ 快速训练", command=self.quick_ev_training)
        training_menu.add_separator()
        training_menu.add_command(label="🔧 超参数优化", command=self.optimize_hyperparameters)
        training_menu.add_command(label="📊 加载训练数据", command=self.load_training_data)
        training_menu.add_separator()
        training_menu.add_command(label="💾 保存模型", command=self.save_trained_model)
        training_menu.add_command(label="📁 加载模型", command=self.load_trained_model)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据质量检查", command=self.check_data_quality)
        tools_menu.add_command(label="模型诊断", command=self.model_diagnosis)
        tools_menu.add_command(label="性能测试", command=self.performance_test)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="用户手册", command=self.show_user_manual)
        help_menu.add_command(label="关于", command=self.show_about)
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # 快速开始按钮
        self.quick_start_btn = ttk.Button(
            toolbar, text="🚀 快速开始", 
            command=self.quick_start,
            style="Accent.TButton"
        )
        self.quick_start_btn.pack(side=tk.LEFT, padx=2)
        
        # 停止按钮
        self.stop_btn = ttk.Button(
            toolbar, text="⏹ 停止", 
            command=self.stop_training,
            state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        # EV训练快捷按钮
        self.ev_training_btn = ttk.Button(
            toolbar, text="🚀 EV训练", 
            command=self.show_ev_training_panel,
            style="Accent.TButton"
        )
        self.ev_training_btn.pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 保存按钮
        ttk.Button(
            toolbar, text="💾 保存", 
            command=self.save_project
        ).pack(side=tk.LEFT, padx=2)
        
        # 加载按钮
        ttk.Button(
            toolbar, text="📁 加载", 
            command=self.open_project
        ).pack(side=tk.LEFT, padx=2)
        
        # 导出按钮
        ttk.Button(
            toolbar, text="📤 导出", 
            command=self.export_results
        ).pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # GPU状态指示器
        self.gpu_status_label = ttk.Label(toolbar, text="GPU: 检测中...")
        self.gpu_status_label.pack(side=tk.RIGHT, padx=5)
        
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建各个面板
        self.parameter_panel = ParameterPanel(self.notebook, self)
        self.data_panel = DataPanel(self.notebook, self)
        self.training_panel = TrainingPanel(self.notebook, self)
        self.ev_training_panel = EVTrainingPanel(self.notebook, self)
        self.prediction_panel = PredictionPanel(self.notebook, self)
        self.metrics_panel = MetricsPanel(self.notebook, self)
        self.status_panel = StatusPanel(self.notebook, self)
        self.visualization_panel = VisualizationPanel(self.notebook, self)
        
        # 添加标签页（按逻辑顺序排列，EV训练放在最后）
        self.notebook.add(self.parameter_panel.frame, text="⚙️ 参数设置")
        self.notebook.add(self.data_panel.frame, text="📊 数据管理")
        self.notebook.add(self.training_panel.frame, text="🎯 训练监控")
        self.notebook.add(self.prediction_panel.frame, text="📈 预测结果")
        self.notebook.add(self.metrics_panel.frame, text="📋 指标显示")
        self.notebook.add(self.status_panel.frame, text="🔍 状态监控")
        self.notebook.add(self.visualization_panel.frame, text="📉 数据可视化")
        self.notebook.add(self.ev_training_panel.frame, text="🚀 EV模型训练")
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态信息
        self.status_label = ttk.Label(self.status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar(master=self.status_frame)
        self.progress_bar = ttk.Progressbar(
            self.status_frame,
            variable=self.progress_var,
            length=200
        )
        self.progress_bar.pack(side=tk.LEFT, padx=5)
        
        # 时间显示
        self.time_label = ttk.Label(self.status_frame, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        
        # 更新时间显示
        self.update_time()
        
    def check_gpu_status(self):
        """检查GPU状态"""
        def check():
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_name = torch.cuda.get_device_name(0)
                    self.gpu_status_label.config(text=f"GPU: {gpu_name[:20]}...")
                else:
                    self.gpu_status_label.config(text="GPU: 不可用")
            except:
                self.gpu_status_label.config(text="GPU: 未知")
                
        threading.Thread(target=check, daemon=True).start()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def process_messages(self):
        """处理消息队列 - 高频率处理以确保实时性"""
        processed_count = 0
        max_batch_size = 50  # 每次最多处理50条消息，防止界面卡顿
        
        try:
            while processed_count < max_batch_size:
                message = self.message_queue.get_nowait()
                self.handle_message(message)
                processed_count += 1
        except queue.Empty:
            pass
        except Exception as e:
            print(f"消息处理错误: {e}")
        finally:
            # 如果处理了很多消息，缩短间隔以提高响应性
            interval = 20 if processed_count > 10 else 50
            self.root.after(interval, self.process_messages)
            
    def handle_message(self, message):
        """处理消息 - 增强版消息处理机制"""
        msg_type = message.get('type')
        
        try:
            if msg_type == 'status':
                text = message.get('text', '')
                self.status_label.config(text=text)
                # 同时更新到状态面板
                if hasattr(self, 'status_panel') and hasattr(self.status_panel, 'add_log'):
                    self.status_panel.add_log(f"状态: {text}")
                    
            elif msg_type == 'progress':
                progress_value = message.get('value', 0)
                self.progress_var.set(progress_value)
                # 更新训练面板进度
                if hasattr(self, 'training_panel'):
                    if hasattr(self.training_panel, 'update_progress'):
                        self.training_panel.update_progress(progress_value, message.get('message', ''))
                        
            elif msg_type == 'training_log':
                # 专门处理训练日志
                log_text = message.get('text', '')
                if hasattr(self, 'status_panel') and hasattr(self.status_panel, 'add_log'):
                    self.status_panel.add_log(f"训练: {log_text}")
                    
            elif msg_type == 'system_info':
                # 处理系统信息更新
                info_data = message.get('data', {})
                if hasattr(self, 'status_panel'):
                    if hasattr(self.status_panel, 'update_system_info'):
                        self.status_panel.update_system_info(info_data)
                        
            elif msg_type == 'gpu_info':
                # 处理GPU信息更新
                gpu_data = message.get('data', {})
                if hasattr(self, 'status_panel'):
                    if hasattr(self.status_panel, 'update_gpu_info_data'):
                        self.status_panel.update_gpu_info_data(gpu_data)
                        
            elif msg_type == 'model_state':
                # 处理模型状态更新
                state = message.get('state', '')
                if hasattr(self, 'status_panel'):
                    if hasattr(self.status_panel, 'model_status_var'):
                        self.status_panel.model_status_var.set(state)
                        
            elif msg_type == 'error':
                error_text = message.get('text', '未知错误')
                messagebox.showerror("错误", error_text)
                # 同时记录到日志
                if hasattr(self, 'status_panel') and hasattr(self.status_panel, 'add_log'):
                    self.status_panel.add_log(f"❌ 错误: {error_text}")
                    
            elif msg_type == 'info':
                info_text = message.get('text', '')
                if message.get('show_dialog', True):
                    messagebox.showinfo("信息", info_text)
                # 记录到日志
                if hasattr(self, 'status_panel') and hasattr(self.status_panel, 'add_log'):
                    self.status_panel.add_log(f"ℹ️ 信息: {info_text}")
                    
            elif msg_type == 'warning':
                # 处理警告信息
                warning_text = message.get('text', '')
                if message.get('show_dialog', False):
                    messagebox.showwarning("警告", warning_text)
                if hasattr(self, 'status_panel') and hasattr(self.status_panel, 'add_log'):
                    self.status_panel.add_log(f"⚠️ 警告: {warning_text}")
                    
        except Exception as e:
            print(f"处理消息时出错 ({msg_type}): {e}")
            
    # 菜单和工具栏事件处理方法
    def new_project(self):
        """新建项目"""
        self.config_manager.reset_to_default()
        self.parameter_panel.load_config(self.config_manager.get_config())
        self.status_label.config(text="新建项目")
        
    def open_project(self):
        """打开项目"""
        file_path = filedialog.askopenfilename(
            title="选择项目文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                self.config_manager.load_config(file_path)
                self.parameter_panel.load_config(self.config_manager.get_config())
                self.status_label.config(text=f"已加载项目: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"加载项目失败: {str(e)}")
                
    def save_project(self):
        """保存项目"""
        file_path = filedialog.asksaveasfilename(
            title="保存项目",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                config = self.parameter_panel.get_config()
                self.config_manager.save_config(config, file_path)
                self.status_label.config(text=f"已保存项目: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"保存项目失败: {str(e)}")
                
    def import_data(self):
        """导入数据"""
        self.notebook.select(1)  # 切换到数据管理面板
        self.data_panel.import_data()
        
    def export_results(self):
        """导出结果"""
        self.notebook.select(3)  # 切换到预测结果面板
        self.prediction_panel.export_results()
        
    def show_parameter_panel(self):
        """显示参数设置面板"""
        self.notebook.select(0)
        
    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要重置所有配置到默认值吗？"):
            self.config_manager.reset_to_default()
            self.parameter_panel.load_config(self.config_manager.get_config())
            self.status_label.config(text="配置已重置")
            
    def toggle_fullscreen(self):
        """切换全屏"""
        self.root.attributes('-fullscreen', not self.root.attributes('-fullscreen'))
        
    def refresh_all(self):
        """刷新所有面板"""
        self.status_label.config(text="刷新中...")
        # 这里可以添加刷新各个面板的逻辑
        self.status_label.config(text="刷新完成")
        
    def check_data_quality(self):
        """数据质量检查"""
        self.notebook.select(1)  # 切换到数据管理面板
        self.data_panel.check_data_quality()
        
    def model_diagnosis(self):
        """模型诊断"""
        messagebox.showinfo("模型诊断", "模型诊断功能开发中...")
        
    def performance_test(self):
        """性能测试"""
        messagebox.showinfo("性能测试", "性能测试功能开发中...")
        
    def show_user_manual(self):
        """显示用户手册"""
        messagebox.showinfo("用户手册", "用户手册功能开发中...")
        
    def show_about(self):
        """显示关于信息"""
        about_text = """
电动汽车充电负荷预测系统 v1.0 Enhanced

基于深度学习的智能预测系统，集成了：
• GRU和Transformer混合架构
• 麻雀搜索算法优化  
• 变分模态分解技术
• 并行计算支持
• 实时GUI同步更新
• 完整的EV模型训练流程

开发：AI Assistant
版本：1.0.0 Enhanced
        """
        messagebox.showinfo("关于", about_text)
    
    # EV训练相关方法
    def show_ev_training_panel(self):
        """显示EV训练面板"""
        if hasattr(self, 'ev_training_panel'):
            # 切换到EV训练面板
            for i, tab_id in enumerate(self.notebook.tabs()):
                if self.notebook.tab(tab_id, "text") == "🚀 EV模型训练":
                    self.notebook.select(i)
                    break
        else:
            messagebox.showwarning("警告", "EV训练面板未初始化")
    
    def quick_ev_training(self):
        """快速EV训练"""
        try:
            if hasattr(self, 'ev_training_panel'):
                # 切换到EV训练面板
                self.show_ev_training_panel()
                
                # 检查是否有数据
                if not self.ev_training_panel.ev_trainer.training_data:
                    # 尝试加载默认数据
                    self.ev_training_panel.ev_trainer.load_data()
                
                # 开始快速训练
                if self.ev_training_panel.ev_trainer.training_data:
                    self.ev_training_panel.start_training()
                else:
                    messagebox.showwarning("警告", "请先加载训练数据")
            else:
                messagebox.showerror("错误", "EV训练面板未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"快速训练失败: {str(e)}")
    
    def optimize_hyperparameters(self):
        """优化超参数"""
        try:
            if hasattr(self, 'ev_training_panel'):
                self.show_ev_training_panel()
                self.ev_training_panel.optimize_hyperparameters()
            else:
                messagebox.showerror("错误", "EV训练面板未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"超参数优化失败: {str(e)}")
    
    def load_training_data(self):
        """加载训练数据"""
        try:
            if hasattr(self, 'ev_training_panel'):
                self.show_ev_training_panel()
                self.ev_training_panel.load_data()
            else:
                messagebox.showerror("错误", "EV训练面板未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"加载训练数据失败: {str(e)}")
    
    def save_trained_model(self):
        """保存训练好的模型"""
        try:
            if hasattr(self, 'ev_training_panel'):
                ev_trainer = self.ev_training_panel.ev_trainer
                if ev_trainer.trained_model:
                    file_path = filedialog.asksaveasfilename(
                        title="保存训练模型",
                        defaultextension=".pth",
                        filetypes=[
                            ("PyTorch模型", "*.pth"),
                            ("所有文件", "*.*")
                        ]
                    )
                    if file_path:
                        success = ev_trainer.save_model(file_path)
                        if success:
                            messagebox.showinfo("成功", f"模型已保存到: {file_path}")
                        else:
                            messagebox.showerror("错误", "模型保存失败")
                else:
                    messagebox.showwarning("警告", "没有训练好的模型可保存")
            else:
                messagebox.showerror("错误", "EV训练面板未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"保存模型失败: {str(e)}")
    
    def load_trained_model(self):
        """加载训练好的模型"""
        try:
            if hasattr(self, 'ev_training_panel'):
                file_path = filedialog.askopenfilename(
                    title="加载训练模型",
                    filetypes=[
                        ("PyTorch模型", "*.pth"),
                        ("所有文件", "*.*")
                    ]
                )
                if file_path:
                    ev_trainer = self.ev_training_panel.ev_trainer
                    success = ev_trainer.load_model(file_path)
                    if success:
                        messagebox.showinfo("成功", f"模型已从 {file_path} 加载")
                        self.show_ev_training_panel()
                    else:
                        messagebox.showerror("错误", "模型加载失败")
            else:
                messagebox.showerror("错误", "EV训练面板未初始化")
        except Exception as e:
            messagebox.showerror("错误", f"加载模型失败: {str(e)}")
        
    def quick_start(self):
        """快速开始"""
        if not self.data_manager.has_data():
            messagebox.showwarning("警告", "请先导入数据！")
            self.notebook.select(1)  # 切换到数据管理面板
            return
            
        # 使用默认配置开始训练
        config = self.config_manager.get_config()
        self.start_training(config)
        
    def start_training(self, config):
        """开始训练"""
        if self.is_training:
            messagebox.showwarning("警告", "训练正在进行中！")
            return
            
        self.is_training = True
        self.quick_start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        # 切换到训练监控面板
        self.notebook.select(2)
        
        # 在后台线程中开始训练
        training_thread = threading.Thread(
            target=self._training_worker,
            args=(config,),
            daemon=True
        )
        training_thread.start()
        
    def _training_worker(self, config):
        """训练工作线程"""
        try:
            self.message_queue.put({'type': 'status', 'text': '开始训练...'})
            
            # 这里调用实际的训练逻辑
            result = self.model_manager.train_model(
                config=config,
                data=self.data_manager.get_processed_data(),
                progress_callback=self._training_progress_callback,
                log_callback=self._training_log_callback
            )
            
            self.message_queue.put({'type': 'status', 'text': '训练完成'})
            self.message_queue.put({'type': 'info', 'text': '模型训练成功完成！'})
            
        except Exception as e:
            self.message_queue.put({'type': 'error', 'text': f'训练失败: {str(e)}'})
        finally:
            self.is_training = False
            self.root.after(0, self._training_finished)
            
    def _training_progress_callback(self, progress, message=""):
        """训练进度回调 - 增强版实时同步"""
        try:
            if self.is_gui_ready():
                # 使用实时同步管理器处理进度更新
                if hasattr(self, 'sync_manager'):
                    self.sync_manager.sync_training_progress(progress, message)
                else:
                    # fallback到原有消息队列方式
                    self.message_queue.put({'type': 'progress', 'value': progress, 'message': message})
                    if message:
                        self.message_queue.put({'type': 'status', 'text': message})
        except Exception as e:
            print(f"训练进度回调错误: {e}")

    def _training_log_callback(self, log_message):
        """训练日志回调（线程安全）- 增强版实时同步"""
        try:
            # 使用实时同步管理器处理日志
            if hasattr(self, 'sync_manager'):
                self.sync_manager.sync_log_output(log_message, "TRAINING")
            else:
                # fallback到原有方式
                log_manager = get_log_manager()
                log_manager.log(log_message)
                
                # 同时更新状态面板
                if self.is_gui_ready() and hasattr(self, 'status_panel'):
                    self.status_panel.add_log(log_message)
        except Exception as e:
            print(f"训练日志回调错误: {e}")
        
    def _training_finished(self):
        """训练完成后的清理工作"""
        self.quick_start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.progress_var.set(0)
        
    def stop_training(self):
        """停止训练"""
        if messagebox.askyesno("确认", "确定要停止训练吗？"):
            self.model_manager.stop_training()
            self.is_training = False
            self._training_finished()
            self.status_label.config(text="训练已停止")
            
    def on_closing(self):
        """关闭程序"""
        if self.is_training:
            if not messagebox.askyesno("确认", "训练正在进行中，确定要退出吗？"):
                return
                
        self.root.quit()
        self.root.destroy()
        
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


if __name__ == "__main__":
    app = EVChargingPredictionGUI()
    app.run()
