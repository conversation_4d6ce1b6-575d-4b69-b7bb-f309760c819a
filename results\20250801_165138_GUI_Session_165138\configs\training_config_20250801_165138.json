{"model_params": {"hidden_size": 128, "num_layers": 2, "dropout": 0.2, "model_type": "gru", "loss_type": "mse", "alpha": 1.0}, "training_params": {"num_epochs": 50, "batch_size": 32, "learning_rate": 0.001, "weight_decay": 0.0001, "patience": 10, "validation_split": 0.1}, "data_params": {"sequence_length": 24, "train_ratio": 0.7, "val_ratio": 0.1, "test_ratio": 0.2, "normalize_features": true, "handle_missing": true, "remove_outliers": true}, "ssa_params": {"use_ssa_optimization": true, "pop_size": 8, "max_iter": 5, "use_parallel": true, "n_workers": 4}, "vmd_params": {"use_vmd": true, "alpha": 2000, "tau": 0, "K": 3, "DC": 0, "init": 1, "tol": 1e-07}, "visualization_params": {"figure_size": [12, 8], "dpi": 100, "style": "seaborn", "color_palette": "viridis", "show_grid": true, "save_plots": true}, "system_params": {"use_gpu": true, "gpu_memory_fraction": 0.8, "random_seed": 42, "log_level": "INFO", "save_checkpoints": true, "checkpoint_interval": 10}, "gui_params": {"theme": "default", "font_size": 10, "auto_refresh": true, "refresh_interval": 1000, "show_tooltips": true, "remember_window_size": true}, "hidden_size": 512, "num_layers": 3, "dropout": 0.15, "learning_rate": 0.003673, "batch_size": 32, "loss_type": "wmse", "weight_decay": 0.000138, "alpha": 1.98, "model_type": "gru"}