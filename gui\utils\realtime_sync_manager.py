#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI实时同步管理器
Real-time Synchronization Manager for GUI and Terminal
"""

import threading
import time
import queue
from typing import Dict, List, Callable, Any, Optional
from .log_manager import get_log_manager
from .threading_utils import global_event_bus, global_update_manager


class RealTimeSyncManager:
    """实时同步管理器 - 统一协调GUI与终端的实时更新"""
    
    def __init__(self):
        self.gui_components = {}
        self.sync_tasks = {}
        self.message_queue = queue.Queue()
        self.is_active = False
        self.lock = threading.Lock()
        self.log_manager = None
        
        # 性能监控
        self.performance_stats = {
            'messages_processed': 0,
            'sync_errors': 0,
            'last_sync_time': 0,
            'avg_sync_interval': 0
        }
        
    def initialize(self, main_app):
        """初始化同步管理器"""
        self.main_app = main_app
        self.log_manager = get_log_manager()
        
        # 注册核心同步任务
        self._register_core_sync_tasks()
        
        # 启动同步服务
        self.start_sync_service()
        
        print("✅ 实时同步管理器已初始化")
    
    def register_gui_component(self, component_id: str, component, sync_methods: List[str]):
        """注册GUI组件进行实时同步"""
        with self.lock:
            self.gui_components[component_id] = {
                'component': component,
                'sync_methods': sync_methods,
                'last_sync': 0,
                'error_count': 0
            }
            
        print(f"✅ 已注册GUI组件: {component_id}")
    
    def unregister_gui_component(self, component_id: str):
        """注销GUI组件"""
        with self.lock:
            if component_id in self.gui_components:
                del self.gui_components[component_id]
                print(f"✅ 已注销GUI组件: {component_id}")
    
    def start_sync_service(self):
        """启动同步服务"""
        if not self.is_active:
            self.is_active = True
            
            # 启动消息处理线程
            self.message_thread = threading.Thread(target=self._message_processor, daemon=True)
            self.message_thread.start()
            
            # 启动定期同步线程
            self.sync_thread = threading.Thread(target=self._periodic_sync, daemon=True)
            self.sync_thread.start()
            
            # 启动全局更新管理器
            global_update_manager.start()
            
            print("✅ 实时同步服务已启动")
    
    def stop_sync_service(self):
        """停止同步服务"""
        self.is_active = False
        global_update_manager.stop()
        print("✅ 实时同步服务已停止")
    
    def send_update_message(self, msg_type: str, data: Any, priority: int = 1):
        """发送更新消息"""
        message = {
            'type': msg_type,
            'data': data,
            'priority': priority,
            'timestamp': time.time()
        }
        
        try:
            self.message_queue.put_nowait(message)
        except queue.Full:
            print(f"⚠️ 消息队列已满，丢弃消息: {msg_type}")
    
    def sync_training_progress(self, progress: float, message: str = "", metrics: Dict = None):
        """同步训练进度"""
        sync_data = {
            'progress': progress,
            'message': message,
            'metrics': metrics or {},
            'timestamp': time.time()
        }
        
        self.send_update_message('training_progress', sync_data, priority=2)
        
        # 发布事件
        global_event_bus.publish('training_progress_updated', sync_data)
    
    def sync_metrics(self, metrics_data: Dict):
        """同步性能指标数据"""
        try:
            print(f"📊 同步管理器.sync_metrics 接收数据: {metrics_data}")
            
            # 包装数据
            data = {
                'type': 'metrics_update',
                'timestamp': time.time(),
                'metrics_data': metrics_data
            }
            
            # 添加到队列
            self.message_queue.put(data) # Changed from update_queue to message_queue
            print(f"📊 指标数据已加入同步队列: {metrics_data}")
            
        except Exception as e:
            print(f"❌ 同步指标数据失败: {e}")

    def sync_log_message(self, log_message: str, log_level: str = "info"):
        """同步日志消息"""
        try:
            print(f"📝 同步管理器.sync_log_message: {log_message}")
            
            data = {
                'type': 'log_message',
                'timestamp': time.time(),
                'message': log_message,
                'level': log_level
            }
            
            self.message_queue.put(data) # Changed from update_queue to message_queue
            
        except Exception as e:
            print(f"❌ 同步日志消息失败: {e}")
    
    def sync_training_status(self, status_data: Dict):
        """同步训练状态"""
        try:
            print(f"🔄 同步管理器.sync_training_status: {status_data}")
            
            data = {
                'type': 'training_status',
                'timestamp': time.time(),
                'status_data': status_data
            }
            
            self.message_queue.put(data) # Changed from update_queue to message_queue
            
        except Exception as e:
            print(f"❌ 同步训练状态失败: {e}")
    
    def sync_terminal_output(self, output_data: Dict):
        """同步终端输出"""
        try:
            print(f"🖥️ 同步管理器.sync_terminal_output: {output_data}")
            
            data = {
                'type': 'terminal_output',
                'timestamp': time.time(),
                'output_data': output_data
            }
            
            self.message_queue.put(data) # Changed from update_queue to message_queue
            
        except Exception as e:
            print(f"❌ 同步终端输出失败: {e}")
            
    def _start_update_worker(self):
        """启动更新工作线程"""
        def worker():
            while self.is_active: # Changed from self.active to self.is_active
                try:
                    # 获取更新数据，设置超时避免阻塞
                    data = self.message_queue.get(timeout=0.1) # Changed from update_queue to message_queue
                    
                    # 处理不同类型的更新
                    if data['type'] == 'metrics_update':
                        self._update_metrics(data)
                    elif data['type'] == 'log_message':
                        self._update_log(data)
                    elif data['type'] == 'training_status':
                        self._update_status(data)
                    elif data['type'] == 'terminal_output':
                        self._update_terminal(data)
                    # 删除训练曲线处理分支
                    # elif data['type'] == 'training_curve':
                    #     self._update_training_curve(data)
                    else:
                        print(f"⚠️ 未知的同步数据类型: {data['type']}")
                    
                    self.message_queue.task_done() # Changed from update_queue to message_queue
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"❌ 同步工作线程错误: {e}")
                    
        # 启动工作线程
        self.message_thread = threading.Thread(target=worker, daemon=True) # Changed from worker_thread to message_thread
        self.message_thread.start()
        print("✅ 同步管理器工作线程已启动（无训练曲线功能）")

    def _update_metrics(self, data):
        """更新性能指标显示"""
        try:
            print(f"📊 同步管理器._update_metrics 接收数据: {data}")
            
            metrics_data = data['metrics_data']
            
            # 通知所有注册的组件
            for component_id, component_info in self.gui_components.items(): # Changed from component_name to component_id
                try:
                    component = component_info['component']
                    if hasattr(component, 'update_metrics_display'):
                        print(f"📊 通知组件 {component_id} 更新指标")
                        component.update_metrics_display(metrics_data)
                    else:
                        # 尝试访问组件的方法
                        if isinstance(component, dict):
                            for comp_id, comp in component.items(): # Changed from component_name to comp_id
                                if hasattr(comp, 'update_metrics_display'):
                                    comp.update_metrics_display(metrics_data)
                        elif hasattr(component, 'update_metrics_display'):
                            component.update_metrics_display(data['metrics_data'])
                            
                except Exception as e:
                    print(f"⚠️ 更新组件 {component_id} 指标失败: {e}")
                    
        except Exception as e:
            print(f"❌ 更新指标显示失败: {e}")

    # 删除训练曲线更新方法
    # def _update_training_curve(self, data):
    #     """更新训练曲线"""
    #     # 此方法已删除
            
    def _update_log(self, data):
        """更新日志显示"""
        try:
            print(f"📝 同步管理器._update_log 接收数据: {data}")
            
            # 通知所有组件更新日志
            for component_id, component_info in self.gui_components.items(): # Changed from component_name to component_id
                try:
                    component = component_info['component']
                    if hasattr(component, 'add_log'):
                        component.add_log(data['message'])
                    elif isinstance(component, dict):
                        for comp_id, comp in component.items(): # Changed from component_name to comp_id
                            if hasattr(comp, 'add_log'):
                                comp.add_log(data['message'])
                                
                except Exception as e:
                    print(f"⚠️ 更新组件 {component_id} 日志失败: {e}")
                    
        except Exception as e:
            print(f"❌ 更新日志显示失败: {e}")
            
    def _update_status(self, data):
        """更新训练状态"""
        try:
            print(f"🔄 同步管理器._update_status 接收数据: {data}")
            
            status_data = data['status_data']
            
            # 通知所有组件更新状态
            for component_id, component_info in self.gui_components.items(): # Changed from component_name to component_id
                try:
                    component = component_info['component']
                    if hasattr(component, 'update_training_status'):
                        component.update_training_status(status_data)
                    elif isinstance(component, dict):
                        for comp_id, comp in component.items(): # Changed from component_name to comp_id
                            if hasattr(comp, 'update_training_status'):
                                comp.update_training_status(status_data)
                                
                except Exception as e:
                    print(f"⚠️ 更新组件 {component_id} 状态失败: {e}")
                    
        except Exception as e:
            print(f"❌ 更新训练状态失败: {e}")
            
    def _update_terminal(self, data):
        """更新终端输出"""
        try:
            print(f"🖥️ 同步管理器._update_terminal 接收数据: {data}")
            
            output_data = data['output_data']
            
            # 通知所有组件更新终端输出
            for component_id, component_info in self.gui_components.items(): # Changed from component_name to component_id
                try:
                    component = component_info['component']
                    if hasattr(component, 'add_terminal_log'):
                        component.add_terminal_log(output_data.get('message', ''), 
                                                 output_data.get('type', 'info'))
                    elif isinstance(component, dict):
                        for comp_id, comp in component.items(): # Changed from component_name to comp_id
                            if hasattr(comp, 'add_terminal_log'):
                                comp.add_terminal_log(output_data.get('message', ''), 
                                                    output_data.get('type', 'info'))
                                
                except Exception as e:
                    print(f"⚠️ 更新组件 {component_id} 终端输出失败: {e}")
                    
        except Exception as e:
            print(f"❌ 更新终端输出失败: {e}")
    
    def _register_core_sync_tasks(self):
        """注册核心同步任务"""
        # 注册系统监控任务
        global_update_manager.register_update_task(
            'system_monitor',
            self._monitor_system_resources,
            interval_ms=2000  # 每2秒监控一次
        )
        
        # 注册GUI状态同步任务
        global_update_manager.register_update_task(
            'gui_sync',
            self._sync_gui_components,
            interval_ms=500   # 每500ms同步一次
        )
        
        # 注册性能统计任务
        global_update_manager.register_update_task(
            'performance_stats',
            self._update_performance_stats,
            interval_ms=5000  # 每5秒更新一次
        )
    
    def _message_processor(self):
        """消息处理线程"""
        while self.is_active:
            try:
                # 按优先级处理消息
                messages = []
                
                # 收集一批消息
                try:
                    while len(messages) < 20:  # 最多处理20条消息
                        message = self.message_queue.get_nowait()
                        messages.append(message)
                except queue.Empty:
                    pass
                
                if messages:
                    # 按优先级排序（优先级越高越先处理）
                    messages.sort(key=lambda x: x['priority'], reverse=True)
                    
                    # 处理消息
                    for message in messages:
                        self._handle_sync_message(message)
                        self.performance_stats['messages_processed'] += 1
                
                time.sleep(0.02)  # 20ms间隔
                
            except Exception as e:
                print(f"消息处理错误: {e}")
                self.performance_stats['sync_errors'] += 1
                time.sleep(0.1)
    
    def _handle_sync_message(self, message):
        """处理同步消息"""
        msg_type = message['type']
        data = message['data']
        
        try:
            if msg_type == 'training_progress':
                self._update_training_progress(data)
            elif msg_type == 'reset_training_history':
                self._reset_training_history(data)
            elif msg_type == 'system_status':
                self._update_system_status(data)
            elif msg_type == 'log_output':
                self._update_log_display(data)
            elif msg_type == 'model_state':
                self._update_model_state(data)
                
        except Exception as e:
            print(f"处理同步消息错误 ({msg_type}): {e}")
            self.performance_stats['sync_errors'] += 1
    
    def _update_training_progress(self, data):
        """更新训练进度显示"""
        with self.lock:
            # 更新训练面板和EV训练面板
            for panel_id in ['training_panel', 'ev_training_panel']:
                if panel_id in self.gui_components:
                    component_info = self.gui_components[panel_id]
                    component = component_info['component']
                    
                    if hasattr(component, 'update_progress'):
                        # 使用线程安全的方式更新GUI
                        try:
                            if hasattr(component, 'frame') and hasattr(component.frame, 'after'):
                                # 创建安全的更新函数
                                def safe_progress_update(comp=component, progress=data['progress'], message=data['message']):
                                    if hasattr(comp, 'update_progress'):
                                        comp.update_progress(progress, message)
                                component.frame.after(0, safe_progress_update)
                            else:
                                component.update_progress(data['progress'], data['message'])
                        except Exception as e:
                            print(f"训练进度更新错误 ({panel_id}): {e}")
    
    def _reset_training_history(self, data):
        """重置训练历史数据"""
        with self.lock:
            # 重置EV训练面板的训练历史
            if 'ev_training_panel' in self.gui_components:
                component_info = self.gui_components['ev_training_panel']
                component = component_info['component']
                
                if hasattr(component, 'reset_training_history'):
                    try:
                        if hasattr(component, 'frame') and hasattr(component.frame, 'after'):
                            # 创建安全的重置函数
                            def safe_reset(comp=component):
                                if hasattr(comp, 'reset_training_history'):
                                    comp.reset_training_history()
                            component.frame.after(0, safe_reset)
                        else:
                            component.reset_training_history()
                    except Exception as e:
                        print(f"重置训练历史错误: {e}")
    
    def _update_system_status(self, data):
        """更新系统状态显示"""
        with self.lock:
            # 更新状态面板
            if 'status_panel' in self.gui_components:
                component_info = self.gui_components['status_panel']
                component = component_info['component']
                
                if hasattr(component, 'update_system_info_data'):
                    # 使用线程安全的方式更新GUI
                    try:
                        if hasattr(component, 'frame') and hasattr(component.frame, 'after'):
                            # 创建安全的更新函数
                            def safe_system_update(comp=component, status_data=data):
                                if hasattr(comp, 'update_system_info_data'):
                                    comp.update_system_info_data(status_data)
                            component.frame.after(0, safe_system_update)
                        else:
                            component.update_system_info_data(data)
                    except Exception as e:
                        print(f"系统状态更新错误: {e}")
    
    def _update_log_display(self, data):
        """更新日志显示"""
        with self.lock:
            # 更新所有支持日志的面板
            for panel_id in ['status_panel', 'ev_training_panel']:
                if panel_id in self.gui_components:
                    component_info = self.gui_components[panel_id]
                    component = component_info['component']
                    
                    if hasattr(component, 'add_log'):
                        # 使用线程安全的方式更新GUI
                        try:
                            if hasattr(component, 'frame') and hasattr(component.frame, 'after'):
                                # 创建安全的日志更新函数
                                def safe_log_update(comp=component, message=data['message']):
                                    if hasattr(comp, 'add_log'):
                                        comp.add_log(message)
                                component.frame.after(0, safe_log_update)
                            else:
                                component.add_log(data['message'])
                        except Exception as e:
                            print(f"日志显示更新错误 ({panel_id}): {e}")
    
    def _update_model_state(self, data):
        """更新模型状态显示"""
        with self.lock:
            # 只更新需要显示模型状态的组件
            model_status_components = ['status_panel', 'training_panel', 'ev_training_panel']
            
            for component_id in model_status_components:
                if component_id in self.gui_components:
                    component_info = self.gui_components[component_id]
                    component = component_info['component']
                    
                    # 检查组件是否有模型状态变量
                    if hasattr(component, 'model_status_var'):
                        # 使用线程安全的方式更新GUI
                        try:
                            if hasattr(component, 'frame') and hasattr(component.frame, 'after'):
                                # 创建一个安全的更新函数，避免闭包问题
                                def safe_update(comp=component, state=data['state']):
                                    if hasattr(comp, 'model_status_var') and comp.model_status_var:
                                        comp.model_status_var.set(state)
                                component.frame.after(0, safe_update)
                            else:
                                component.model_status_var.set(data['state'])
                        except Exception as e:
                            print(f"模型状态更新错误 ({component_id}): {e}")
                    # 如果组件没有model_status_var，静默跳过（这是正常的）
    
    def _monitor_system_resources(self):
        """监控系统资源"""
        try:
            import psutil
            
            cpu_usage = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            
            # 获取GPU信息
            gpu_info = {}
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_count = torch.cuda.device_count()
                    for i in range(gpu_count):
                        memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
                        memory_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                        gpu_info[f'gpu_{i}'] = {
                            'memory_allocated': memory_allocated,
                            'memory_total': memory_total,
                            'utilization': (memory_allocated / memory_total) * 100
                        }
            except ImportError:
                pass
            
            # 同步系统状态
            self.send_update_message('system_status', {
                'cpu_usage': cpu_usage,
                'memory_usage': memory.percent,
                'gpu_info': gpu_info
            }, priority=1)
            
        except Exception as e:
            print(f"系统资源监控错误: {e}")
    
    def _sync_gui_components(self):
        """同步GUI组件（线程安全版本）"""
        current_time = time.time()
        
        with self.lock:
            components_to_sync = list(self.gui_components.items())
        
        # 在锁外进行组件同步，避免死锁
        for component_id, component_info in components_to_sync:
            try:
                component = component_info['component']
                sync_methods = component_info['sync_methods']
                
                # 检查组件是否还有效
                if not hasattr(component, 'frame'):
                    continue
                
                try:
                    # 检查frame是否还存在
                    if hasattr(component.frame, 'winfo_exists') and not component.frame.winfo_exists():
                        continue
                except:
                    # frame可能已经被销毁，跳过
                    continue
                
                # 调用组件的同步方法（线程安全）
                for method_name in sync_methods:
                    if hasattr(component, method_name):
                        try:
                            method = getattr(component, method_name)
                            if callable(method):
                                # 使用线程安全的方式调用
                                if hasattr(component, 'frame') and hasattr(component.frame, 'after'):
                                    component.frame.after(0, method)
                                else:
                                    method()
                        except Exception as e:
                            print(f"同步方法调用错误 ({component_id}.{method_name}): {e}")
                
                # 更新同步时间
                with self.lock:
                    if component_id in self.gui_components:
                        self.gui_components[component_id]['last_sync'] = current_time
                
            except Exception as e:
                print(f"GUI组件同步错误 ({component_id}): {e}")
                # 增加错误计数
                with self.lock:
                    if component_id in self.gui_components:
                        self.gui_components[component_id]['error_count'] += 1
    
    def _periodic_sync(self):
        """定期同步检查"""
        while self.is_active:
            try:
                current_time = time.time()
                
                # 检查是否有组件长时间未同步
                with self.lock:
                    for component_id, component_info in self.gui_components.items():
                        last_sync = component_info['last_sync']
                        if current_time - last_sync > 5.0:  # 超过5秒未同步
                            print(f"⚠️ 组件 {component_id} 长时间未同步")
                
                time.sleep(1.0)  # 每秒检查一次
                
            except Exception as e:
                print(f"定期同步检查错误: {e}")
                time.sleep(1.0)
    
    def _update_performance_stats(self):
        """更新性能统计"""
        current_time = time.time()
        
        if self.performance_stats['last_sync_time'] > 0:
            interval = current_time - self.performance_stats['last_sync_time']
            
            # 计算平均同步间隔
            if self.performance_stats['avg_sync_interval'] == 0:
                self.performance_stats['avg_sync_interval'] = interval
            else:
                # 使用指数移动平均
                alpha = 0.1
                self.performance_stats['avg_sync_interval'] = (
                    alpha * interval + (1 - alpha) * self.performance_stats['avg_sync_interval']
                )
        
        self.performance_stats['last_sync_time'] = current_time
    
    def get_performance_stats(self):
        """获取性能统计信息"""
        return self.performance_stats.copy()


# 全局实时同步管理器实例
_sync_manager = None

def get_sync_manager() -> RealTimeSyncManager:
    """获取全局同步管理器实例"""
    global _sync_manager
    if _sync_manager is None:
        _sync_manager = RealTimeSyncManager()
    return _sync_manager


def initialize_sync_manager(main_app):
    """初始化同步管理器"""
    sync_manager = get_sync_manager()
    sync_manager.initialize(main_app)
    return sync_manager 