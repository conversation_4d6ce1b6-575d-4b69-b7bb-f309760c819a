import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Dict, Any
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

# 导入字体配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from gui.utils.gui_utils import setup_all_fonts, get_font_manager
from gui.utils.log_manager import get_log_manager, setup_training_logging
from gui.managers.results_manager import get_results_manager

# 设置字体
setup_all_fonts()


class TrainingPanel:
    """训练面板类"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)
        self.training_thread = None
        self.is_training = False
        self.training_data = []

        # 获取字体管理器
        try:
            self.font_manager = get_font_manager()
        except:
            self.font_manager = None

        self.create_widgets()
        
        # 启动实时训练状态更新
        self.start_real_time_training_updates()

    def start_real_time_training_updates(self):
        """启动实时训练状态更新"""
        self.update_training_status()
        
    def update_training_status(self):
        """实时更新训练状态"""
        try:
            # 检查训练状态
            if hasattr(self.main_app, 'is_training'):
                if self.main_app.is_training:
                    self.status_var.set("训练中...")
                    self.start_button.config(state=tk.DISABLED)
                    self.stop_button.config(state=tk.NORMAL)
                    self.pause_button.config(state=tk.NORMAL)
                else:
                    self.status_var.set("已停止")
                    self.start_button.config(state=tk.NORMAL)
                    self.stop_button.config(state=tk.DISABLED)
                    self.pause_button.config(state=tk.DISABLED)
            
            # 检查模型状态
            if hasattr(self.main_app, 'model_manager'):
                if hasattr(self.main_app.model_manager, 'is_model_available'):
                    if self.main_app.model_manager.is_model_available():
                        if not hasattr(self.main_app, 'is_training') or not self.main_app.is_training:
                            self.status_var.set("就绪")
        
        except Exception as e:
            print(f"训练状态更新错误: {e}")
        finally:
            # 每500ms检查一次训练状态
            self.frame.after(500, self.update_training_status)
    
    def update_progress(self, progress_value, message=""):
        """更新训练进度 - 线程安全"""
        try:
            # 安全地更新进度条
            if hasattr(self, 'overall_progress_var'):
                self.overall_progress_var.set(progress_value)
            
            # 更新进度信息
            if message and hasattr(self, 'progress_info_var'):
                self.progress_info_var.set(message)
            
        except Exception as e:
            print(f"进度更新错误: {e}")

    def _get_font(self, font_type="default"):
        """获取字体的辅助方法"""
        if self.font_manager:
            if font_type == "title":
                return self.font_manager.get_title_font()
            elif font_type == "label":
                return self.font_manager.get_label_font()
            elif font_type == "button":
                return self.font_manager.get_button_font()
            else:
                return self.font_manager.get_default_font()
        return None
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主容器
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：训练控制
        left_frame = ttk.Frame(main_container)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧：训练监控
        right_frame = ttk.Frame(main_container)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建各个区域
        self.create_control_section(left_frame)
        self.create_monitoring_section(right_frame)
        
    def create_control_section(self, parent):
        """创建训练控制区域"""
        # 训练状态
        status_frame = ttk.LabelFrame(parent, text="🎯 训练状态", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_var = tk.StringVar(value="未开始")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, font=self._get_font("title"))
        status_label.pack()
        
        # 训练控制按钮
        control_frame = ttk.LabelFrame(parent, text="⚙️ 训练控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text="▶️ 开始训练", 
                                      command=self.start_training,
                                      style="Accent.TButton")
        self.start_button.pack(fill=tk.X, pady=(0, 5))
        
        self.stop_button = ttk.Button(control_frame, text="⏹️ 停止训练", 
                                     command=self.stop_training,
                                     state=tk.DISABLED)
        self.stop_button.pack(fill=tk.X, pady=(0, 5))
        
        self.pause_button = ttk.Button(control_frame, text="⏸️ 暂停训练", 
                                      command=self.pause_training,
                                      state=tk.DISABLED)
        self.pause_button.pack(fill=tk.X)
        
        # 训练进度
        progress_frame = ttk.LabelFrame(parent, text="📊 训练进度", padding=10)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 总体进度
        ttk.Label(progress_frame, text="总体进度:").pack(anchor=tk.W)
        self.overall_progress_var = tk.DoubleVar()
        self.overall_progress = ttk.Progressbar(progress_frame, 
                                              variable=self.overall_progress_var,
                                              maximum=100, length=200)
        self.overall_progress.pack(fill=tk.X, pady=(0, 5))
        
        # 当前轮次进度
        ttk.Label(progress_frame, text="当前轮次:").pack(anchor=tk.W)
        self.epoch_progress_var = tk.DoubleVar()
        self.epoch_progress = ttk.Progressbar(progress_frame, 
                                            variable=self.epoch_progress_var,
                                            maximum=100, length=200)
        self.epoch_progress.pack(fill=tk.X, pady=(0, 5))
        
        # 进度信息
        self.progress_info_var = tk.StringVar(master=progress_frame, value="等待开始...")
        progress_info_label = ttk.Label(progress_frame, textvariable=self.progress_info_var)
        progress_info_label.pack()

        # 训练参数快速设置
        params_frame = ttk.LabelFrame(parent, text="⚡ 快速设置", padding=10)
        params_frame.pack(fill=tk.X, pady=(0, 10))

        # 训练轮数
        epochs_frame = ttk.Frame(params_frame)
        epochs_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(epochs_frame, text="训练轮数:").pack(side=tk.LEFT)
        self.epochs_var = tk.IntVar(master=epochs_frame, value=50)
        ttk.Spinbox(epochs_frame, from_=1, to=1000, width=10,
                   textvariable=self.epochs_var).pack(side=tk.RIGHT)

        # 批次大小
        batch_frame = ttk.Frame(params_frame)
        batch_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(batch_frame, text="批次大小:").pack(side=tk.LEFT)
        self.batch_size_var = tk.IntVar(master=batch_frame, value=32)
        ttk.Spinbox(batch_frame, from_=1, to=512, width=10,
                   textvariable=self.batch_size_var).pack(side=tk.RIGHT)

        # 学习率
        lr_frame = ttk.Frame(params_frame)
        lr_frame.pack(fill=tk.X)
        ttk.Label(lr_frame, text="学习率:").pack(side=tk.LEFT)
        self.learning_rate_var = tk.DoubleVar(master=lr_frame, value=0.001)
        ttk.Entry(lr_frame, textvariable=self.learning_rate_var, width=12).pack(side=tk.RIGHT)
        
        # 训练历史
        history_frame = ttk.LabelFrame(parent, text="📜 训练历史", padding=10)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        # 历史列表
        self.history_tree = ttk.Treeview(history_frame, 
                                        columns=('时间', '轮数', '最佳损失'), 
                                        show='headings', height=8)
        
        self.history_tree.heading('时间', text='时间')
        self.history_tree.heading('轮数', text='轮数')
        self.history_tree.heading('最佳损失', text='最佳损失')
        
        self.history_tree.column('时间', width=120)
        self.history_tree.column('轮数', width=60)
        self.history_tree.column('最佳损失', width=100)
        
        self.history_tree.pack(fill=tk.BOTH, expand=True)
        
        # 历史操作按钮
        history_buttons = ttk.Frame(history_frame)
        history_buttons.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(history_buttons, text="🔄 刷新", 
                  command=self.refresh_history).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(history_buttons, text="🗑️ 清空", 
                  command=self.clear_history).pack(side=tk.LEFT)
        
    def create_monitoring_section(self, parent):
        """创建训练监控区域"""
        # 创建笔记本控件
        self.monitor_notebook = ttk.Notebook(parent)
        self.monitor_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 损失曲线标签页
        self.create_loss_plot_tab()
        
        # 训练日志标签页
        self.create_log_tab()
        
        # 实时指标标签页
        self.create_metrics_tab()
        
    def create_loss_plot_tab(self):
        """创建损失曲线标签页"""
        plot_tab = ttk.Frame(self.monitor_notebook)
        self.monitor_notebook.add(plot_tab, text="📈 损失曲线")

        # 确保matplotlib字体配置
        setup_all_fonts()

        # 获取字体管理器
        font_manager = get_font_manager()

        # 创建matplotlib图表
        self.fig, self.ax = plt.subplots(figsize=(8, 6))

        # 设置中文字体属性
        font_prop = {'family': font_manager.matplotlib_font, 'size': 12}
        label_font_prop = {'family': font_manager.matplotlib_font, 'size': 10}
        legend_font_prop = {'family': font_manager.matplotlib_font, 'size': 9}

        # 设置标题和坐标轴标签，明确指定字体
        self.ax.set_title('训练和验证损失', fontdict=font_prop)
        self.ax.set_xlabel('轮次', fontdict=label_font_prop)
        self.ax.set_ylabel('损失值', fontdict=label_font_prop)
        self.ax.grid(True, alpha=0.3)

        # 初始化空线条
        self.train_line, = self.ax.plot([], [], 'b-', label='训练损失', linewidth=2)
        self.val_line, = self.ax.plot([], [], 'r-', label='验证损失', linewidth=2)

        # 设置图例，明确指定字体
        self.ax.legend(prop=legend_font_prop)
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, plot_tab)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        toolbar_frame = ttk.Frame(plot_tab)
        toolbar_frame.pack(fill=tk.X)
        
        ttk.Button(toolbar_frame, text="🔄 刷新图表", 
                  command=self.refresh_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="💾 保存图表", 
                  command=self.save_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🔍 自动缩放", 
                  command=self.auto_scale_plot).pack(side=tk.LEFT)
        
    def create_log_tab(self):
        """创建训练日志标签页"""
        log_tab = ttk.Frame(self.monitor_notebook)
        self.monitor_notebook.add(log_tab, text="📝 训练日志")
        
        # 创建文本框
        self.log_text = tk.Text(log_tab, wrap=tk.WORD, height=20)
        log_scrollbar = ttk.Scrollbar(log_tab, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志控制按钮
        log_controls = ttk.Frame(log_tab)
        log_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(log_controls, text="🗑️ 清空日志", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_controls, text="💾 保存日志", 
                  command=self.save_log).pack(side=tk.LEFT, padx=(0, 5))
        
        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(master=log_controls, value=True)
        ttk.Checkbutton(log_controls, text="自动滚动",
                       variable=self.auto_scroll_var).pack(side=tk.RIGHT)
        
    def create_metrics_tab(self):
        """创建实时指标标签页"""
        metrics_tab = ttk.Frame(self.monitor_notebook)
        self.monitor_notebook.add(metrics_tab, text="📊 实时指标")
        
        # 指标显示区域
        metrics_frame = ttk.LabelFrame(metrics_tab, text="当前指标", padding=10)
        metrics_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 创建指标标签
        metrics_grid = ttk.Frame(metrics_frame)
        metrics_grid.pack(fill=tk.X)
        
        # 当前轮次
        ttk.Label(metrics_grid, text="当前轮次:").grid(row=0, column=0, sticky='w', padx=(0, 10))
        self.current_epoch_var = tk.StringVar(value="0/0")
        ttk.Label(metrics_grid, textvariable=self.current_epoch_var, 
                 font=("Arial", 10, "bold")).grid(row=0, column=1, sticky='w')
        
        # 训练损失
        ttk.Label(metrics_grid, text="训练损失:").grid(row=1, column=0, sticky='w', padx=(0, 10))
        self.train_loss_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.train_loss_var, 
                 font=("Arial", 10, "bold")).grid(row=1, column=1, sticky='w')
        
        # 验证损失
        ttk.Label(metrics_grid, text="验证损失:").grid(row=2, column=0, sticky='w', padx=(0, 10))
        self.val_loss_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.val_loss_var, 
                 font=("Arial", 10, "bold")).grid(row=2, column=1, sticky='w')
        
        # 最佳损失
        ttk.Label(metrics_grid, text="最佳损失:").grid(row=0, column=2, sticky='w', padx=(20, 10))
        self.best_loss_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.best_loss_var, 
                 font=("Arial", 10, "bold")).grid(row=0, column=3, sticky='w')
        
        # 学习率
        ttk.Label(metrics_grid, text="学习率:").grid(row=1, column=2, sticky='w', padx=(20, 10))
        self.current_lr_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.current_lr_var, 
                 font=("Arial", 10, "bold")).grid(row=1, column=3, sticky='w')
        
        # 训练时间
        ttk.Label(metrics_grid, text="训练时间:").grid(row=2, column=2, sticky='w', padx=(20, 10))
        self.training_time_var = tk.StringVar(value="00:00:00")
        ttk.Label(metrics_grid, textvariable=self.training_time_var, 
                 font=("Arial", 10, "bold")).grid(row=2, column=3, sticky='w')
        
        # 配置网格权重
        for i in range(4):
            metrics_grid.columnconfigure(i, weight=1)
        
    def start_training(self):
        """开始训练"""
        # 检查数据是否准备好
        if not self.main_app.data_manager.has_processed_data():
            messagebox.showerror("错误", "请先加载并预处理数据")
            return
        
        # 获取训练配置
        config = self.get_training_config()
        
        # 更新界面状态
        self.is_training = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.NORMAL)
        self.status_var.set("训练中...")
        
        # 清空之前的训练数据
        self.training_data = []
        self.clear_plot()
        
        # 记录开始时间
        self.training_start_time = time.time()
        
        # 设置统一日志系统
        log_manager = get_log_manager()

        # 正确获取状态面板的日志回调
        status_panel_callback = None
        if hasattr(self.main_app, 'status_panel') and self.main_app.status_panel:
            if hasattr(self.main_app.status_panel, 'add_log'):
                status_panel_callback = self.main_app.status_panel.add_log
                self.add_log_message("✅ 状态面板日志回调已连接")
            else:
                self.add_log_message("⚠️ 状态面板缺少add_log方法")
        else:
            self.add_log_message("⚠️ 状态面板未找到")

        setup_training_logging(
            training_panel_callback=self.add_log_message,
            status_panel_callback=status_panel_callback
        )

        # 验证日志系统设置
        self.add_log_message("🔧 训练日志系统已初始化")
        if status_panel_callback:
            self.add_log_message("📊 状态监控日志同步已启用")

        # 在后台线程中开始训练
        def training_thread():
            try:
                data = self.main_app.data_manager.get_processed_data()
                result = self.main_app.model_manager.train_model(
                    config, data,
                    progress_callback=self.update_training_progress,
                    log_callback=self.add_log_message
                )

                # 训练完成
                self.frame.after(0, lambda: self.on_training_completed(result))

            except Exception as e:
                self.frame.after(0, lambda: self.on_training_error(str(e)))
        
        self.training_thread = threading.Thread(target=training_thread, daemon=True)
        self.training_thread.start()
        
        # 启动定时器更新界面
        self.update_training_timer()
        
    def stop_training(self):
        """停止训练"""
        if self.is_training:
            self.main_app.model_manager.stop_training()
            self.is_training = False
            self.update_training_buttons()
            self.status_var.set("已停止")
            self.add_log_message("训练被用户停止")
            
    def pause_training(self):
        """暂停训练（暂时不实现）"""
        messagebox.showinfo("提示", "暂停功能暂未实现")
        
    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置"""
        config = self.main_app.config_manager.get_config()
        
        # 使用界面上的快速设置覆盖配置
        config['training_params']['num_epochs'] = self.epochs_var.get()
        config['training_params']['batch_size'] = self.batch_size_var.get()
        config['training_params']['learning_rate'] = self.learning_rate_var.get()
        
        return config
        
    def update_training_progress(self, progress: float, message: str):
        """更新训练进度（线程安全）"""
        try:
            # 检查GUI是否已准备好
            if not hasattr(self.main_app, 'gui_initialized') or not self.main_app.gui_initialized:
                return

            # 使用after方法确保在主线程中更新GUI
            if hasattr(self, 'frame') and self.frame.winfo_exists():
                self.frame.after(0, lambda: self._update_progress_safe(progress, message))
        except Exception as e:
            # 如果GUI还没有准备好，忽略更新
            pass

    def _update_progress_safe(self, progress: float, message: str):
        """安全更新进度（在主线程中执行）"""
        try:
            self.overall_progress_var.set(progress)
            self.progress_info_var.set(message)
        except Exception as e:
            print(f"进度更新失败: {e}")
        
    def add_log_message(self, message: str):
        """添加日志消息（线程安全）"""
        try:
            # 检查GUI是否已准备好
            if not hasattr(self.main_app, 'gui_initialized') or not self.main_app.gui_initialized:
                return

            # 使用after方法确保在主线程中更新GUI
            if hasattr(self, 'frame') and self.frame.winfo_exists():
                self.frame.after(0, lambda: self._add_log_safe(message))
        except Exception as e:
            # 如果GUI还没有准备好，忽略更新
            pass

    def _add_log_safe(self, message: str):
        """安全添加日志（在主线程中执行）"""
        try:
            timestamp = time.strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            self.log_text.insert(tk.END, log_entry)

            if self.auto_scroll_var.get():
                self.log_text.see(tk.END)
        except Exception as e:
            print(f"日志添加失败: {e}")
            
    def update_training_timer(self):
        """更新训练计时器"""
        if self.is_training:
            elapsed = time.time() - self.training_start_time
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)
            seconds = int(elapsed % 60)
            
            time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.training_time_var.set(time_str)
            
            # 每秒更新一次
            self.frame.after(1000, self.update_training_timer)
            
    def on_training_completed(self, result: Dict[str, Any]):
        """训练完成处理"""
        self.is_training = False
        self.update_training_buttons()
        self.status_var.set("训练完成")
        
        if result.get('status') == 'completed':
            self.add_log_message("训练成功完成")
            messagebox.showinfo("成功", "模型训练完成")
            
            # 更新训练历史
            self.refresh_history()
        else:
            self.add_log_message("训练未正常完成")
            
    def on_training_error(self, error_message: str):
        """训练错误处理"""
        self.is_training = False
        self.update_training_buttons()
        self.status_var.set("训练失败")
        self.add_log_message(f"训练失败: {error_message}")
        messagebox.showerror("错误", f"训练失败: {error_message}")
        
    def update_training_buttons(self):
        """更新训练按钮状态"""
        if self.is_training:
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.NORMAL)
        else:
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.DISABLED)
            
    def refresh_history(self):
        """刷新训练历史"""
        # 清空现有项目
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # 获取训练历史
        history = self.main_app.model_manager.get_training_history()
        
        for record in history:
            timestamp = record.get('timestamp', 'N/A')
            total_epochs = record.get('training_result', {}).get('total_epochs', 'N/A')
            best_loss = record.get('training_result', {}).get('best_val_loss', 'N/A')
            
            if isinstance(best_loss, float):
                best_loss = f"{best_loss:.6f}"
            
            self.history_tree.insert('', 'end', values=(timestamp, total_epochs, best_loss))
            
    def clear_history(self):
        """清空训练历史"""
        if messagebox.askyesno("确认", "确定要清空训练历史吗？"):
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
                
    def clear_plot(self):
        """清空损失曲线图"""
        self.train_line.set_data([], [])
        self.val_line.set_data([], [])
        self.ax.relim()
        self.ax.autoscale_view()
        self.canvas.draw()
        
    def refresh_plot(self):
        """刷新损失曲线图"""
        # 这里应该从训练数据中更新图表
        # 暂时使用模拟数据
        if self.training_data:
            epochs = [d['epoch'] for d in self.training_data]
            train_losses = [d['train_loss'] for d in self.training_data]
            val_losses = [d['val_loss'] for d in self.training_data]
            
            self.train_line.set_data(epochs, train_losses)
            self.val_line.set_data(epochs, val_losses)
            
            self.ax.relim()
            self.ax.autoscale_view()
            self.canvas.draw()
            
    def save_plot(self):
        """保存损失曲线图"""
        try:
            # 使用结果管理器自动保存到当前会话目录
            results_manager = get_results_manager()
            plot_path = results_manager.save_figure(self.fig, "training_loss_curve")
            messagebox.showinfo("成功", f"训练损失曲线已保存到: {plot_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

    def save_plot_manual(self):
        """手动选择路径保存损失曲线图"""
        from tkinter import filedialog

        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[
                ("PNG文件", "*.png"),
                ("PDF文件", "*.pdf"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.fig.savefig(file_path, dpi=300, bbox_inches='tight')
            messagebox.showinfo("成功", f"图表已保存到: {file_path}")
            
    def auto_scale_plot(self):
        """自动缩放图表"""
        self.ax.relim()
        self.ax.autoscale_view()
        self.canvas.draw()
        
    def clear_log(self):
        """清空训练日志"""
        self.log_text.delete(1.0, tk.END)
        
    def save_log(self):
        """保存训练日志"""
        from tkinter import filedialog
        
        file_path = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {str(e)}")
    
    def get_frame(self):
        """获取面板框架"""
        return self.frame
