"""
数据管理面板 - 提供数据导入、预览和预处理功能
Data Panel - Provides data import, preview and preprocessing functionality
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import threading
from typing import Optional
import os
import sys

# 添加项目路径以导入字体管理器
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from gui.utils.gui_utils import get_font_manager


class DataPanel:
    """数据管理面板类"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)
        self.current_data = None

        # 获取字体管理器
        try:
            self.font_manager = get_font_manager()
        except:
            self.font_manager = None

        self.create_widgets()

    def _get_font(self, font_type="default"):
        """获取字体的辅助方法"""
        if self.font_manager:
            if font_type == "title":
                return self.font_manager.get_title_font()
            elif font_type == "label":
                return self.font_manager.get_label_font()
            elif font_type == "button":
                return self.font_manager.get_button_font()
            else:
                return self.font_manager.get_default_font()
        return None

    def create_widgets(self):
        """创建界面组件"""
        # 创建主容器
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部：数据导入区域
        self.create_import_section(main_container)
        
        # 中部：数据预览和信息
        self.create_preview_section(main_container)
        
        # 底部：数据预处理控制
        self.create_preprocessing_section(main_container)
        
    def create_import_section(self, parent):
        """创建数据导入区域"""
        import_frame = ttk.LabelFrame(parent, text="📁 数据导入", padding=10)
        import_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择行
        file_frame = ttk.Frame(import_frame)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        label_font = self._get_font("label")
        ttk.Label(file_frame, text="数据文件:", font=label_font).pack(side=tk.LEFT)
        
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, padx=(10, 5), fill=tk.X, expand=True)
        
        ttk.Button(file_frame, text="浏览", 
                  command=self.browse_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_frame, text="加载数据", 
                  command=self.load_data,
                  style="Accent.TButton").pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(import_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        # 状态标签
        self.status_var = tk.StringVar(value="请选择数据文件")
        self.status_label = ttk.Label(import_frame, textvariable=self.status_var)
        self.status_label.pack()
        
    def create_preview_section(self, parent):
        """创建数据预览区域"""
        preview_frame = ttk.LabelFrame(parent, text="📊 数据预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建笔记本控件用于多标签页
        self.preview_notebook = ttk.Notebook(preview_frame)
        self.preview_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 数据预览标签页
        self.create_data_preview_tab()
        
        # 数据信息标签页
        self.create_data_info_tab()
        
        # 数据质量标签页
        self.create_data_quality_tab()
        
    def create_data_preview_tab(self):
        """创建数据预览标签页"""
        preview_tab = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(preview_tab, text="数据预览")
        
        # 创建表格
        columns = ['列1', '列2', '列3', '列4', '列5']  # 默认列名
        self.data_tree = ttk.Treeview(preview_tab, columns=columns, show='headings', height=15)
        
        # 设置列标题
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(preview_tab, orient=tk.VERTICAL, command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(preview_tab, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_data_info_tab(self):
        """创建数据信息标签页"""
        info_tab = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(info_tab, text="数据信息")
        
        # 创建文本框显示数据信息
        self.info_text = tk.Text(info_tab, wrap=tk.WORD, height=15)
        info_scrollbar = ttk.Scrollbar(info_tab, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_data_quality_tab(self):
        """创建数据质量标签页"""
        quality_tab = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(quality_tab, text="数据质量")
        
        # 创建文本框显示质量报告
        self.quality_text = tk.Text(quality_tab, wrap=tk.WORD, height=15)
        quality_scrollbar = ttk.Scrollbar(quality_tab, orient=tk.VERTICAL, command=self.quality_text.yview)
        self.quality_text.configure(yscrollcommand=quality_scrollbar.set)
        
        self.quality_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        quality_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_preprocessing_section(self, parent):
        """创建数据预处理区域"""
        preprocess_frame = ttk.LabelFrame(parent, text="⚙️ 数据预处理", padding=10)
        preprocess_frame.pack(fill=tk.X)
        
        # 预处理选项
        options_frame = ttk.Frame(preprocess_frame)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧选项
        left_options = ttk.Frame(options_frame)
        left_options.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.normalize_var = tk.BooleanVar(master=left_options, value=True)
        ttk.Checkbutton(left_options, text="特征归一化",
                       variable=self.normalize_var).pack(anchor=tk.W)

        self.handle_missing_var = tk.BooleanVar(master=left_options, value=True)
        ttk.Checkbutton(left_options, text="处理缺失值",
                       variable=self.handle_missing_var).pack(anchor=tk.W)

        # 右侧选项
        right_options = ttk.Frame(options_frame)
        right_options.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        self.remove_outliers_var = tk.BooleanVar(master=right_options, value=False)
        ttk.Checkbutton(right_options, text="移除异常值",
                       variable=self.remove_outliers_var).pack(anchor=tk.W)

        # 序列长度设置
        seq_frame = ttk.Frame(right_options)
        seq_frame.pack(anchor=tk.W, pady=(5, 0))

        ttk.Label(seq_frame, text="序列长度:").pack(side=tk.LEFT)
        self.sequence_length_var = tk.IntVar(master=seq_frame, value=24)
        ttk.Spinbox(seq_frame, from_=1, to=168, width=10,
                   textvariable=self.sequence_length_var).pack(side=tk.LEFT, padx=(5, 0))
        
        # 操作按钮
        button_frame = ttk.Frame(preprocess_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="🔄 预处理数据", 
                  command=self.preprocess_data,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="💾 导出数据", 
                  command=self.export_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🗑️ 清空数据", 
                  command=self.clear_data).pack(side=tk.LEFT)
        
        # 预处理进度条
        self.preprocess_progress_var = tk.DoubleVar()
        self.preprocess_progress_bar = ttk.Progressbar(preprocess_frame, 
                                                     variable=self.preprocess_progress_var,
                                                     maximum=100, length=400)
        self.preprocess_progress_bar.pack(fill=tk.X, pady=(10, 0))
        
    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx;*.xls"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.file_path_var.set(file_path)
            
    def load_data(self):
        """加载数据"""
        file_path = self.file_path_var.get().strip()
        
        if not file_path:
            messagebox.showerror("错误", "请先选择数据文件")
            return
        
        # 在后台线程中加载数据
        def load_thread():
            try:
                success = self.main_app.data_manager.load_data(
                    file_path, 
                    progress_callback=self.update_progress
                )
                
                if success:
                    # 在主线程中更新界面
                    self.frame.after(0, self.on_data_loaded)
                else:
                    self.frame.after(0, lambda: messagebox.showerror("错误", "数据加载失败"))
                    
            except Exception as e:
                self.frame.after(0, lambda: messagebox.showerror("错误", f"数据加载失败: {str(e)}"))
        
        threading.Thread(target=load_thread, daemon=True).start()
        
    def update_progress(self, value: float, message: str):
        """更新进度"""
        try:
            # 检查GUI是否已准备好
            if not hasattr(self.main_app, 'gui_initialized') or not self.main_app.gui_initialized:
                return

            # 使用after方法确保在主线程中更新GUI
            if hasattr(self, 'frame') and self.frame.winfo_exists():
                self.frame.after(0, lambda: self._update_progress_safe(value, message))
        except Exception as e:
            # 如果GUI还没有准备好，忽略更新
            pass

    def _update_progress_safe(self, value: float, message: str):
        """安全地更新进度（在主线程中）"""
        try:
            if hasattr(self, 'progress_var') and hasattr(self, 'status_var'):
                self.progress_var.set(value)
                self.status_var.set(message)
        except Exception as e:
            pass
        
    def on_data_loaded(self):
        """数据加载完成后的处理"""
        self.status_var.set("数据加载完成")
        self.progress_var.set(100)
        
        # 更新数据预览
        self.update_data_preview()
        
        # 更新数据信息
        self.update_data_info()
        
        # 更新数据质量报告
        self.update_data_quality()
        
        messagebox.showinfo("成功", "数据加载完成")
        
    def update_data_preview(self):
        """更新数据预览"""
        try:
            # 检查数据管理器是否存在
            if not hasattr(self.main_app, 'data_manager') or self.main_app.data_manager is None:
                print("数据管理器未初始化")
                return

            # 检查是否有数据
            if not self.main_app.data_manager.has_data():
                print("没有可用数据")
                return

            data = self.main_app.data_manager.get_data_preview(100)

            if data is None or data.empty:
                print("数据预览为空")
                return

            # 清空现有数据
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # 更新列标题
            columns = list(data.columns)
            self.data_tree['columns'] = columns

            for col in columns:
                self.data_tree.heading(col, text=col)
                self.data_tree.column(col, width=120)

            # 添加数据行
            for index, row in data.iterrows():
                try:
                    values = [str(val) for val in row.values]
                    self.data_tree.insert('', 'end', values=values)
                except Exception as row_error:
                    print(f"添加数据行时出错: {row_error}")
                    continue

            print(f"数据预览更新完成，显示 {len(data)} 行数据")

        except Exception as e:
            print(f"更新数据预览失败: {e}")
            # 清空数据树以避免显示错误数据
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)
            
    def update_data_info(self):
        """更新数据信息"""
        info = self.main_app.data_manager.get_data_info()
        
        self.info_text.delete(1.0, tk.END)
        
        if info:
            info_str = f"""数据基本信息:
            
数据形状: {info.get('shape', 'N/A')}
列数: {len(info.get('columns', []))}
内存使用: {info.get('memory_usage', 0) / 1024 / 1024:.2f} MB

列信息:
"""
            for col in info.get('columns', []):
                dtype = info.get('dtypes', {}).get(col, 'unknown')
                null_count = info.get('null_counts', {}).get(col, 0)
                info_str += f"  {col}: {dtype} (缺失值: {null_count})\n"
            
            self.info_text.insert(1.0, info_str)
            
    def update_data_quality(self):
        """更新数据质量报告"""
        quality = self.main_app.data_manager.get_quality_report()
        
        self.quality_text.delete(1.0, tk.END)
        
        if quality:
            quality_str = f"""数据质量报告:

缺失值分析:
  总缺失值: {quality.get('missing_analysis', {}).get('total_missing', 0)}
  
重复值分析:
  总重复值: {quality.get('duplicate_analysis', {}).get('total_duplicates', 0)}
  重复比例: {quality.get('duplicate_analysis', {}).get('duplicate_percentage', 0):.2f}%

异常值分析:
"""
            outlier_analysis = quality.get('outlier_analysis', {})
            for col, outlier_info in outlier_analysis.items():
                quality_str += f"  {col}: {outlier_info.get('count', 0)} 个异常值 ({outlier_info.get('percentage', 0):.2f}%)\n"
            
            self.quality_text.insert(1.0, quality_str)
            
    def preprocess_data(self):
        """预处理数据"""
        if not self.main_app.data_manager.has_data():
            messagebox.showerror("错误", "请先加载数据")
            return
        
        # 获取预处理配置
        config = {
            'normalize_features': self.normalize_var.get(),
            'handle_missing': self.handle_missing_var.get(),
            'remove_outliers': self.remove_outliers_var.get(),
            'sequence_length': self.sequence_length_var.get()
        }
        
        # 在后台线程中预处理数据
        def preprocess_thread():
            try:
                success = self.main_app.data_manager.preprocess_data(
                    config,
                    progress_callback=self.update_preprocess_progress
                )
                
                if success:
                    self.frame.after(0, lambda: messagebox.showinfo("成功", "数据预处理完成"))
                else:
                    self.frame.after(0, lambda: messagebox.showerror("错误", "数据预处理失败"))
                    
            except Exception as e:
                self.frame.after(0, lambda: messagebox.showerror("错误", f"数据预处理失败: {str(e)}"))
        
        threading.Thread(target=preprocess_thread, daemon=True).start()
        
    def update_preprocess_progress(self, value: float, message: str):
        """更新预处理进度"""
        try:
            # 检查GUI是否已准备好
            if not hasattr(self.main_app, 'gui_initialized') or not self.main_app.gui_initialized:
                return

            # 使用after方法确保在主线程中更新GUI
            if hasattr(self, 'frame') and self.frame.winfo_exists():
                self.frame.after(0, lambda: self._update_preprocess_progress_safe(value, message))
        except Exception as e:
            # 如果GUI还没有准备好，忽略更新
            pass

    def _update_preprocess_progress_safe(self, value: float, message: str):
        """安全地更新预处理进度（在主线程中）"""
        try:
            if hasattr(self, 'preprocess_progress_var'):
                self.preprocess_progress_var.set(value)
        except Exception as e:
            pass
        
    def export_data(self):
        """导出数据"""
        if not self.main_app.data_manager.has_processed_data():
            messagebox.showerror("错误", "请先预处理数据")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="导出数据",
            defaultextension=".json",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                success = self.main_app.data_manager.export_processed_data(file_path)
                if success:
                    messagebox.showinfo("成功", f"数据已导出到: {file_path}")
                else:
                    messagebox.showerror("错误", "数据导出失败")
            except Exception as e:
                messagebox.showerror("错误", f"数据导出失败: {str(e)}")
                
    def clear_data(self):
        """清空数据"""
        if messagebox.askyesno("确认", "确定要清空所有数据吗？"):
            self.main_app.data_manager.clear_data()
            
            # 清空界面
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)
            
            self.info_text.delete(1.0, tk.END)
            self.quality_text.delete(1.0, tk.END)
            
            self.progress_var.set(0)
            self.preprocess_progress_var.set(0)
            self.status_var.set("请选择数据文件")
            
            messagebox.showinfo("成功", "数据已清空")
    
    def get_frame(self):
        """获取面板框架"""
        return self.frame
