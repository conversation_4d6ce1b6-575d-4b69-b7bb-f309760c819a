"""
验证工具函数
Validation Utility Functions
"""

import os
import re
from typing import Any, Dict, List, Union, Optional
import json


class ValidationError(Exception):
    """验证错误异常"""
    pass


def validate_number(value: Any, min_val: Optional[float] = None, 
                   max_val: Optional[float] = None, 
                   allow_none: bool = False) -> float:
    """验证数字"""
    if value is None:
        if allow_none:
            return None
        raise ValidationError("数值不能为空")
    
    try:
        num_val = float(value)
    except (ValueError, TypeError):
        raise ValidationError(f"无效的数值: {value}")
    
    if min_val is not None and num_val < min_val:
        raise ValidationError(f"数值不能小于 {min_val}")
    
    if max_val is not None and num_val > max_val:
        raise ValidationError(f"数值不能大于 {max_val}")
    
    return num_val


def validate_integer(value: Any, min_val: Optional[int] = None, 
                    max_val: Optional[int] = None, 
                    allow_none: bool = False) -> int:
    """验证整数"""
    if value is None:
        if allow_none:
            return None
        raise ValidationError("整数不能为空")
    
    try:
        int_val = int(value)
    except (ValueError, TypeError):
        raise ValidationError(f"无效的整数: {value}")
    
    if min_val is not None and int_val < min_val:
        raise ValidationError(f"整数不能小于 {min_val}")
    
    if max_val is not None and int_val > max_val:
        raise ValidationError(f"整数不能大于 {max_val}")
    
    return int_val


def validate_string(value: Any, min_length: Optional[int] = None, 
                   max_length: Optional[int] = None, 
                   pattern: Optional[str] = None,
                   allow_empty: bool = True) -> str:
    """验证字符串"""
    if value is None:
        if allow_empty:
            return ""
        raise ValidationError("字符串不能为空")
    
    str_val = str(value)
    
    if not allow_empty and not str_val.strip():
        raise ValidationError("字符串不能为空")
    
    if min_length is not None and len(str_val) < min_length:
        raise ValidationError(f"字符串长度不能少于 {min_length} 个字符")
    
    if max_length is not None and len(str_val) > max_length:
        raise ValidationError(f"字符串长度不能超过 {max_length} 个字符")
    
    if pattern is not None and not re.match(pattern, str_val):
        raise ValidationError(f"字符串格式不正确")
    
    return str_val


def validate_file_path(path: str, must_exist: bool = True, 
                      extensions: Optional[List[str]] = None) -> str:
    """验证文件路径"""
    if not path or not isinstance(path, str):
        raise ValidationError("文件路径不能为空")
    
    path = path.strip()
    
    if must_exist and not os.path.exists(path):
        raise ValidationError(f"文件不存在: {path}")
    
    if must_exist and not os.path.isfile(path):
        raise ValidationError(f"路径不是文件: {path}")
    
    if extensions:
        file_ext = os.path.splitext(path)[1].lower()
        if file_ext not in [ext.lower() for ext in extensions]:
            raise ValidationError(f"文件扩展名必须是: {', '.join(extensions)}")
    
    return path


def validate_directory_path(path: str, must_exist: bool = True, 
                           create_if_not_exists: bool = False) -> str:
    """验证目录路径"""
    if not path or not isinstance(path, str):
        raise ValidationError("目录路径不能为空")
    
    path = path.strip()
    
    if must_exist and not os.path.exists(path):
        if create_if_not_exists:
            try:
                os.makedirs(path, exist_ok=True)
            except OSError as e:
                raise ValidationError(f"无法创建目录: {e}")
        else:
            raise ValidationError(f"目录不存在: {path}")
    
    if os.path.exists(path) and not os.path.isdir(path):
        raise ValidationError(f"路径不是目录: {path}")
    
    return path


def validate_email(email: str) -> str:
    """验证邮箱地址"""
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    if not email or not isinstance(email, str):
        raise ValidationError("邮箱地址不能为空")
    
    email = email.strip()
    
    if not re.match(email_pattern, email):
        raise ValidationError("邮箱地址格式不正确")
    
    return email


def validate_url(url: str) -> str:
    """验证URL"""
    url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    
    if not url or not isinstance(url, str):
        raise ValidationError("URL不能为空")
    
    url = url.strip()
    
    if not re.match(url_pattern, url):
        raise ValidationError("URL格式不正确")
    
    return url


def validate_choice(value: Any, choices: List[Any], 
                   allow_none: bool = False) -> Any:
    """验证选择值"""
    if value is None:
        if allow_none:
            return None
        raise ValidationError("选择值不能为空")
    
    if value not in choices:
        raise ValidationError(f"选择值必须是: {', '.join(map(str, choices))}")
    
    return value


def validate_list(value: Any, item_validator: callable = None, 
                 min_length: Optional[int] = None, 
                 max_length: Optional[int] = None) -> List[Any]:
    """验证列表"""
    if not isinstance(value, (list, tuple)):
        raise ValidationError("值必须是列表或元组")
    
    value_list = list(value)
    
    if min_length is not None and len(value_list) < min_length:
        raise ValidationError(f"列表长度不能少于 {min_length}")
    
    if max_length is not None and len(value_list) > max_length:
        raise ValidationError(f"列表长度不能超过 {max_length}")
    
    if item_validator:
        validated_items = []
        for i, item in enumerate(value_list):
            try:
                validated_item = item_validator(item)
                validated_items.append(validated_item)
            except ValidationError as e:
                raise ValidationError(f"列表第 {i+1} 项验证失败: {e}")
        return validated_items
    
    return value_list


def validate_dict(value: Any, schema: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """验证字典
    
    schema格式:
    {
        'key_name': {
            'type': str/int/float/bool,
            'required': True/False,
            'validator': callable,
            'default': default_value
        }
    }
    """
    if not isinstance(value, dict):
        raise ValidationError("值必须是字典")
    
    result = {}
    
    for key, rules in schema.items():
        key_value = value.get(key)
        is_required = rules.get('required', False)
        has_default = 'default' in rules
        
        # 检查必需字段
        if key_value is None:
            if is_required:
                raise ValidationError(f"必需字段 '{key}' 缺失")
            elif has_default:
                key_value = rules['default']
            else:
                continue
        
        # 类型验证
        expected_type = rules.get('type')
        if expected_type and not isinstance(key_value, expected_type):
            try:
                key_value = expected_type(key_value)
            except (ValueError, TypeError):
                raise ValidationError(f"字段 '{key}' 类型错误，期望 {expected_type.__name__}")
        
        # 自定义验证器
        validator = rules.get('validator')
        if validator:
            try:
                key_value = validator(key_value)
            except ValidationError as e:
                raise ValidationError(f"字段 '{key}' 验证失败: {e}")
        
        result[key] = key_value
    
    return result


def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """验证配置字典"""
    
    # 定义配置模式
    config_schema = {
        'model_params': {
            'type': dict,
            'required': True,
            'validator': lambda x: validate_model_params(x)
        },
        'training_params': {
            'type': dict,
            'required': True,
            'validator': lambda x: validate_training_params(x)
        },
        'data_params': {
            'type': dict,
            'required': True,
            'validator': lambda x: validate_data_params(x)
        },
        'ssa_params': {
            'type': dict,
            'required': False,
            'default': {},
            'validator': lambda x: validate_ssa_params(x)
        },
        'vmd_params': {
            'type': dict,
            'required': False,
            'default': {},
            'validator': lambda x: validate_vmd_params(x)
        }
    }
    
    return validate_dict(config, config_schema)


def validate_model_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """验证模型参数"""
    schema = {
        'input_size': {
            'type': int,
            'required': True,
            'validator': lambda x: validate_integer(x, min_val=1)
        },
        'hidden_size': {
            'type': int,
            'required': True,
            'validator': lambda x: validate_integer(x, min_val=1)
        },
        'num_layers': {
            'type': int,
            'required': True,
            'validator': lambda x: validate_integer(x, min_val=1, max_val=10)
        },
        'output_size': {
            'type': int,
            'required': True,
            'validator': lambda x: validate_integer(x, min_val=1)
        },
        'dropout': {
            'type': float,
            'required': False,
            'default': 0.1,
            'validator': lambda x: validate_number(x, min_val=0.0, max_val=1.0)
        }
    }
    
    return validate_dict(params, schema)


def validate_training_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """验证训练参数"""
    schema = {
        'num_epochs': {
            'type': int,
            'required': True,
            'validator': lambda x: validate_integer(x, min_val=1, max_val=10000)
        },
        'batch_size': {
            'type': int,
            'required': True,
            'validator': lambda x: validate_integer(x, min_val=1, max_val=1024)
        },
        'learning_rate': {
            'type': float,
            'required': True,
            'validator': lambda x: validate_number(x, min_val=1e-6, max_val=1.0)
        },
        'weight_decay': {
            'type': float,
            'required': False,
            'default': 0.0,
            'validator': lambda x: validate_number(x, min_val=0.0, max_val=1.0)
        },
        'early_stopping_patience': {
            'type': int,
            'required': False,
            'default': 10,
            'validator': lambda x: validate_integer(x, min_val=1)
        }
    }
    
    return validate_dict(params, schema)


def validate_data_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """验证数据参数"""
    schema = {
        'sequence_length': {
            'type': int,
            'required': True,
            'validator': lambda x: validate_integer(x, min_val=1, max_val=1000)
        },
        'train_ratio': {
            'type': float,
            'required': False,
            'default': 0.7,
            'validator': lambda x: validate_number(x, min_val=0.1, max_val=0.9)
        },
        'val_ratio': {
            'type': float,
            'required': False,
            'default': 0.15,
            'validator': lambda x: validate_number(x, min_val=0.05, max_val=0.5)
        },
        'test_ratio': {
            'type': float,
            'required': False,
            'default': 0.15,
            'validator': lambda x: validate_number(x, min_val=0.05, max_val=0.5)
        },
        'normalize': {
            'type': bool,
            'required': False,
            'default': True
        }
    }
    
    validated = validate_dict(params, schema)
    
    # 验证比例总和
    total_ratio = validated['train_ratio'] + validated['val_ratio'] + validated['test_ratio']
    if abs(total_ratio - 1.0) > 1e-6:
        raise ValidationError("训练、验证和测试比例之和必须等于1.0")
    
    return validated


def validate_ssa_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """验证SSA参数"""
    schema = {
        'population_size': {
            'type': int,
            'required': False,
            'default': 30,
            'validator': lambda x: validate_integer(x, min_val=10, max_val=200)
        },
        'max_iterations': {
            'type': int,
            'required': False,
            'default': 100,
            'validator': lambda x: validate_integer(x, min_val=10, max_val=1000)
        },
        'c1': {
            'type': float,
            'required': False,
            'default': 2.0,
            'validator': lambda x: validate_number(x, min_val=0.1, max_val=10.0)
        },
        'c2': {
            'type': float,
            'required': False,
            'default': 2.0,
            'validator': lambda x: validate_number(x, min_val=0.1, max_val=10.0)
        }
    }
    
    return validate_dict(params, schema)


def validate_vmd_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """验证VMD参数"""
    schema = {
        'alpha': {
            'type': float,
            'required': False,
            'default': 2000.0,
            'validator': lambda x: validate_number(x, min_val=100.0, max_val=10000.0)
        },
        'tau': {
            'type': float,
            'required': False,
            'default': 0.0,
            'validator': lambda x: validate_number(x, min_val=0.0, max_val=1.0)
        },
        'K': {
            'type': int,
            'required': False,
            'default': 5,
            'validator': lambda x: validate_integer(x, min_val=2, max_val=20)
        },
        'DC': {
            'type': bool,
            'required': False,
            'default': False
        }
    }
    
    return validate_dict(params, schema)


def validate_json_file(file_path: str) -> Dict[str, Any]:
    """验证JSON文件"""
    validate_file_path(file_path, must_exist=True, extensions=['.json'])
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except json.JSONDecodeError as e:
        raise ValidationError(f"JSON文件格式错误: {e}")
    except Exception as e:
        raise ValidationError(f"读取JSON文件失败: {e}")


def validate_csv_file(file_path: str) -> str:
    """验证CSV文件"""
    validate_file_path(file_path, must_exist=True, extensions=['.csv'])
    
    try:
        # 简单检查文件是否可读
        with open(file_path, 'r', encoding='utf-8') as f:
            f.readline()  # 读取第一行
        return file_path
    except Exception as e:
        raise ValidationError(f"CSV文件读取失败: {e}")


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
    # 移除或替换非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(illegal_chars, '_', filename)
    
    # 移除前后空格和点
    sanitized = sanitized.strip(' .')
    
    # 确保不为空
    if not sanitized:
        sanitized = 'untitled'
    
    return sanitized


def validate_range(value: float, min_val: float, max_val: float, 
                  name: str = "值") -> float:
    """验证数值范围"""
    if not isinstance(value, (int, float)):
        raise ValidationError(f"{name}必须是数字")
    
    if value < min_val or value > max_val:
        raise ValidationError(f"{name}必须在 {min_val} 到 {max_val} 之间")
    
    return float(value)
