"""
可视化管理器 - 负责图表生成和可视化管理
Visualization Manager - Handles chart generation and visualization management
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
import os
from datetime import datetime
import warnings

# 导入字体配置函数
from ..utils.gui_utils import setup_matplotlib_fonts

# 设置matplotlib中文字体
setup_matplotlib_fonts()

warnings.filterwarnings('ignore')


class VisualizationManager:
    """可视化管理器类"""
    
    def __init__(self):
        self.figure_cache = {}
        self.plot_config = {
            'figure_size': (12, 8),
            'dpi': 100,
            'style': 'seaborn-v0_8',
            'color_palette': 'viridis',
            'font_size': 10
        }
        self.setup_style()
        
    def setup_style(self):
        """设置绘图样式"""
        try:
            plt.style.use(self.plot_config['style'])
        except:
            plt.style.use('default')
            
        sns.set_palette(self.plot_config['color_palette'])
        plt.rcParams.update({'font.size': self.plot_config['font_size']})
    
    def create_training_progress_plot(self, training_history: List[Dict], 
                                    save_path: Optional[str] = None) -> plt.Figure:
        """
        创建训练进度图
        
        Args:
            training_history: 训练历史数据
            save_path: 保存路径（可选）
            
        Returns:
            plt.Figure: 图表对象
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.plot_config['figure_size'])
        
        if not training_history:
            ax1.text(0.5, 0.5, '暂无训练数据', ha='center', va='center', transform=ax1.transAxes)
            ax2.text(0.5, 0.5, '暂无训练数据', ha='center', va='center', transform=ax2.transAxes)
            return fig
        
        # 提取损失数据
        epochs = [item['epoch'] for item in training_history]
        train_losses = [item['train_loss'] for item in training_history]
        val_losses = [item['val_loss'] for item in training_history]
        
        # 绘制损失曲线
        ax1.plot(epochs, train_losses, label='训练损失', marker='o', markersize=4)
        ax1.plot(epochs, val_losses, label='验证损失', marker='s', markersize=4)
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('损失值')
        ax1.set_title('训练和验证损失')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 绘制损失差异
        loss_diff = np.array(val_losses) - np.array(train_losses)
        ax2.plot(epochs, loss_diff, label='验证-训练损失差', color='red', marker='d', markersize=4)
        ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('损失差异')
        ax2.set_title('过拟合监控')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=self.plot_config['dpi'], bbox_inches='tight')
        
        return fig
    
    def create_prediction_comparison_plot(self, y_true: np.ndarray, y_pred: np.ndarray,
                                        timestamps: Optional[np.ndarray] = None,
                                        save_path: Optional[str] = None) -> plt.Figure:
        """
        创建预测对比图
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            timestamps: 时间戳（可选）
            save_path: 保存路径（可选）
            
        Returns:
            plt.Figure: 图表对象
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 时间序列对比图
        if timestamps is not None:
            ax1.plot(timestamps, y_true, label='真实值', alpha=0.7, linewidth=1)
            ax1.plot(timestamps, y_pred, label='预测值', alpha=0.7, linewidth=1)
            ax1.set_xlabel('时间')
        else:
            x_axis = range(len(y_true))
            ax1.plot(x_axis, y_true, label='真实值', alpha=0.7, linewidth=1)
            ax1.plot(x_axis, y_pred, label='预测值', alpha=0.7, linewidth=1)
            ax1.set_xlabel('样本序号')
        
        ax1.set_ylabel('充电负荷 (kW)')
        ax1.set_title('预测值与真实值对比')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 散点图
        ax2.scatter(y_true, y_pred, alpha=0.6, s=20)
        min_val = min(y_true.min(), y_pred.min())
        max_val = max(y_true.max(), y_pred.max())
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
        ax2.set_xlabel('真实值')
        ax2.set_ylabel('预测值')
        ax2.set_title('预测散点图')
        ax2.grid(True, alpha=0.3)
        
        # 误差分布图
        errors = y_pred - y_true
        ax3.hist(errors, bins=50, alpha=0.7, edgecolor='black')
        ax3.axvline(x=0, color='red', linestyle='--', alpha=0.8)
        ax3.set_xlabel('预测误差')
        ax3.set_ylabel('频次')
        ax3.set_title('误差分布直方图')
        ax3.grid(True, alpha=0.3)
        
        # 误差时间序列图
        if timestamps is not None:
            ax4.plot(timestamps, errors, alpha=0.7, linewidth=1)
            ax4.set_xlabel('时间')
        else:
            ax4.plot(range(len(errors)), errors, alpha=0.7, linewidth=1)
            ax4.set_xlabel('样本序号')
        
        ax4.axhline(y=0, color='red', linestyle='--', alpha=0.8)
        ax4.set_ylabel('预测误差')
        ax4.set_title('误差时间序列')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=self.plot_config['dpi'], bbox_inches='tight')
        
        return fig
    
    def create_data_overview_plot(self, data: pd.DataFrame, 
                                save_path: Optional[str] = None) -> plt.Figure:
        """
        创建数据概览图
        
        Args:
            data: 数据DataFrame
            save_path: 保存路径（可选）
            
        Returns:
            plt.Figure: 图表对象
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 数据分布图
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        if len(numeric_columns) > 0:
            main_column = numeric_columns[0]  # 假设第一个数值列是主要目标
            ax1.hist(data[main_column].dropna(), bins=50, alpha=0.7, edgecolor='black')
            ax1.set_xlabel(main_column)
            ax1.set_ylabel('频次')
            ax1.set_title(f'{main_column} 分布')
            ax1.grid(True, alpha=0.3)
        
        # 时间序列图（如果有时间列）
        time_columns = data.select_dtypes(include=['datetime64']).columns
        if len(time_columns) > 0 and len(numeric_columns) > 0:
            time_col = time_columns[0]
            value_col = numeric_columns[0]
            ax2.plot(data[time_col], data[value_col], alpha=0.7, linewidth=1)
            ax2.set_xlabel('时间')
            ax2.set_ylabel(value_col)
            ax2.set_title('时间序列图')
            ax2.grid(True, alpha=0.3)
        else:
            ax2.text(0.5, 0.5, '无时间序列数据', ha='center', va='center', transform=ax2.transAxes)
        
        # 相关性热力图
        if len(numeric_columns) > 1:
            corr_matrix = data[numeric_columns].corr()
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                       square=True, ax=ax3, fmt='.2f')
            ax3.set_title('特征相关性热力图')
        else:
            ax3.text(0.5, 0.5, '特征不足，无法计算相关性', ha='center', va='center', transform=ax3.transAxes)
        
        # 缺失值分析
        missing_data = data.isnull().sum()
        missing_data = missing_data[missing_data > 0]
        if len(missing_data) > 0:
            missing_data.plot(kind='bar', ax=ax4)
            ax4.set_xlabel('列名')
            ax4.set_ylabel('缺失值数量')
            ax4.set_title('缺失值分析')
            ax4.tick_params(axis='x', rotation=45)
        else:
            ax4.text(0.5, 0.5, '无缺失值', ha='center', va='center', transform=ax4.transAxes)
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=self.plot_config['dpi'], bbox_inches='tight')
        
        return fig
    
    def create_metrics_dashboard(self, metrics: Dict[str, float],
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        创建指标仪表板
        
        Args:
            metrics: 指标字典
            save_path: 保存路径（可选）
            
        Returns:
            plt.Figure: 图表对象
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()
        
        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())
        
        # 指标条形图
        if len(metric_names) > 0:
            colors = plt.cm.viridis(np.linspace(0, 1, len(metric_names)))
            bars = axes[0].bar(metric_names, metric_values, color=colors)
            axes[0].set_title('模型性能指标')
            axes[0].set_ylabel('指标值')
            axes[0].tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars, metric_values):
                height = bar.get_height()
                axes[0].text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.4f}', ha='center', va='bottom')
        
        # R²指标的可视化（如果存在）
        if 'r2' in metrics:
            r2_value = metrics['r2']
            theta = np.linspace(0, 2*np.pi, 100)
            r = np.ones_like(theta)
            
            ax_polar = plt.subplot(2, 2, 2, projection='polar')
            ax_polar.fill_between(theta, 0, r2_value, alpha=0.3, color='green' if r2_value > 0.8 else 'orange' if r2_value > 0.6 else 'red')
            ax_polar.set_ylim(0, 1)
            ax_polar.set_title(f'R² = {r2_value:.4f}')
            ax_polar.set_theta_zero_location('N')
            ax_polar.set_theta_direction(-1)
        else:
            axes[1].text(0.5, 0.5, '无R²指标', ha='center', va='center', transform=axes[1].transAxes)
        
        # 误差指标对比（如果有多个误差指标）
        error_metrics = {k: v for k, v in metrics.items() if k.lower() in ['mse', 'rmse', 'mae']}
        if len(error_metrics) > 1:
            error_names = list(error_metrics.keys())
            error_values = list(error_metrics.values())
            
            axes[2].plot(error_names, error_values, marker='o', linewidth=2, markersize=8)
            axes[2].set_title('误差指标趋势')
            axes[2].set_ylabel('误差值')
            axes[2].grid(True, alpha=0.3)
        else:
            axes[2].text(0.5, 0.5, '误差指标不足', ha='center', va='center', transform=axes[2].transAxes)
        
        # 指标雷达图（如果指标足够多）
        if len(metrics) >= 3:
            # 标准化指标值用于雷达图
            normalized_values = []
            for key, value in metrics.items():
                if key.lower() == 'r2':
                    normalized_values.append(max(0, value))  # R²可能为负
                else:
                    # 对于误差指标，取倒数并标准化
                    normalized_values.append(1 / (1 + value))
            
            angles = np.linspace(0, 2*np.pi, len(metrics), endpoint=False).tolist()
            normalized_values += normalized_values[:1]  # 闭合图形
            angles += angles[:1]
            
            ax_radar = plt.subplot(2, 2, 4, projection='polar')
            ax_radar.plot(angles, normalized_values, 'o-', linewidth=2)
            ax_radar.fill(angles, normalized_values, alpha=0.25)
            ax_radar.set_xticks(angles[:-1])
            ax_radar.set_xticklabels(metric_names)
            ax_radar.set_title('指标雷达图')
        else:
            axes[3].text(0.5, 0.5, '指标不足，无法绘制雷达图', ha='center', va='center', transform=axes[3].transAxes)
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=self.plot_config['dpi'], bbox_inches='tight')
        
        return fig
    
    def create_feature_importance_plot(self, feature_names: List[str], 
                                     importance_scores: np.ndarray,
                                     save_path: Optional[str] = None) -> plt.Figure:
        """
        创建特征重要性图
        
        Args:
            feature_names: 特征名称列表
            importance_scores: 重要性分数
            save_path: 保存路径（可选）
            
        Returns:
            plt.Figure: 图表对象
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 排序特征重要性
        sorted_indices = np.argsort(importance_scores)[::-1]
        sorted_features = [feature_names[i] for i in sorted_indices]
        sorted_scores = importance_scores[sorted_indices]
        
        # 水平条形图
        y_pos = np.arange(len(sorted_features))
        ax1.barh(y_pos, sorted_scores, alpha=0.7)
        ax1.set_yticks(y_pos)
        ax1.set_yticklabels(sorted_features)
        ax1.set_xlabel('重要性分数')
        ax1.set_title('特征重要性排序')
        ax1.grid(True, alpha=0.3)
        
        # 饼图（显示前10个最重要的特征）
        top_n = min(10, len(sorted_features))
        top_features = sorted_features[:top_n]
        top_scores = sorted_scores[:top_n]
        
        # 如果有其他特征，归为"其他"
        if len(sorted_features) > top_n:
            other_score = np.sum(sorted_scores[top_n:])
            top_features.append('其他')
            top_scores = np.append(top_scores, other_score)
        
        ax2.pie(top_scores, labels=top_features, autopct='%1.1f%%', startangle=90)
        ax2.set_title(f'前{top_n}个重要特征分布')
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=self.plot_config['dpi'], bbox_inches='tight')
        
        return fig
    
    def save_all_plots(self, plots_dict: Dict[str, plt.Figure], output_dir: str):
        """
        保存所有图表
        
        Args:
            plots_dict: 图表字典 {名称: 图表对象}
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        
        for name, fig in plots_dict.items():
            file_path = os.path.join(output_dir, f"{name}.png")
            fig.savefig(file_path, dpi=self.plot_config['dpi'], bbox_inches='tight')
            print(f"图表已保存: {file_path}")
    
    def update_plot_config(self, new_config: Dict[str, Any]):
        """更新绘图配置"""
        self.plot_config.update(new_config)
        self.setup_style()
    
    def clear_cache(self):
        """清空图表缓存"""
        for fig in self.figure_cache.values():
            plt.close(fig)
        self.figure_cache.clear()
