"""
配置管理器 - 负责配置的保存、加载和管理
Configuration Manager - Handles configuration saving, loading and management
"""

import json
import os
from typing import Dict, Any, Optional
from copy import deepcopy


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self):
        self.default_config = self._get_default_config()
        self.current_config = deepcopy(self.default_config)
        self.config_history = []
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 模型参数
            'model_params': {
                'hidden_size': 128,
                'num_layers': 2,
                'dropout': 0.2,
                'model_type': 'gru',  # 'gru' 或 'transformer_gru'
                'loss_type': 'mse',   # 'mse', 'huber', 'wmse', 'zero_aware', 'adaptive'
                'alpha': 1.0          # 用于加权损失函数
            },
            
            # 训练参数
            'training_params': {
                'num_epochs': 50,
                'batch_size': 32,
                'learning_rate': 0.001,
                'weight_decay': 1e-4,
                'patience': 10,
                'validation_split': 0.1
            },
            
            # 数据参数
            'data_params': {
                'sequence_length': 24,
                'train_ratio': 0.7,
                'val_ratio': 0.1,
                'test_ratio': 0.2,
                'normalize_features': True,
                'handle_missing': True,
                'remove_outliers': True
            },
            
            # SSA优化参数
            'ssa_params': {
                'use_ssa_optimization': True,
                'pop_size': 8,
                'max_iter': 5,
                'use_parallel': True,
                'n_workers': 4
            },
            
            # VMD参数
            'vmd_params': {
                'use_vmd': True,
                'alpha': 2000,
                'tau': 0,
                'K': 3,
                'DC': 0,
                'init': 1,
                'tol': 1e-7
            },
            
            # 可视化参数
            'visualization_params': {
                'figure_size': (12, 8),
                'dpi': 100,
                'style': 'seaborn',
                'color_palette': 'viridis',
                'show_grid': True,
                'save_plots': True
            },
            
            # 系统参数
            'system_params': {
                'use_gpu': True,
                'gpu_memory_fraction': 0.8,
                'random_seed': 42,
                'log_level': 'INFO',
                'save_checkpoints': True,
                'checkpoint_interval': 10
            },
            
            # 界面参数
            'gui_params': {
                'theme': 'default',
                'font_size': 10,
                'auto_refresh': True,
                'refresh_interval': 1000,  # 毫秒
                'show_tooltips': True,
                'remember_window_size': True
            }
        }
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return deepcopy(self.current_config)
    
    def update_config(self, new_config: Dict[str, Any], section: Optional[str] = None):
        """
        更新配置
        
        Args:
            new_config: 新的配置
            section: 配置节名称（可选）
        """
        if section:
            if section in self.current_config:
                self.current_config[section].update(new_config)
            else:
                self.current_config[section] = new_config
        else:
            self._deep_update(self.current_config, new_config)
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.current_config = deepcopy(self.default_config)
    
    def save_config(self, config: Optional[Dict[str, Any]] = None, file_path: str = "config.json"):
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置（可选，默认使用当前配置）
            file_path: 保存路径
        """
        if config is None:
            config = self.current_config
            
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else '.', exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            # 添加到历史记录
            self.config_history.append({
                'file_path': file_path,
                'timestamp': self._get_timestamp(),
                'config': deepcopy(config)
            })
            
        except Exception as e:
            raise Exception(f"保存配置失败: {str(e)}")
    
    def load_config(self, file_path: str):
        """
        从文件加载配置
        
        Args:
            file_path: 配置文件路径
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"配置文件不存在: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
            
            # 验证配置
            validated_config = self._validate_config(loaded_config)
            
            # 更新当前配置
            self.current_config = validated_config
            
            # 添加到历史记录
            self.config_history.append({
                'file_path': file_path,
                'timestamp': self._get_timestamp(),
                'config': deepcopy(validated_config),
                'action': 'load'
            })
            
        except Exception as e:
            raise Exception(f"加载配置失败: {str(e)}")
    
    def _validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证配置的有效性
        
        Args:
            config: 要验证的配置
            
        Returns:
            Dict: 验证后的配置
        """
        # 从默认配置开始
        validated_config = deepcopy(self.default_config)
        
        # 递归更新
        self._deep_update(validated_config, config)
        
        # 进行具体的验证
        self._validate_model_params(validated_config['model_params'])
        self._validate_training_params(validated_config['training_params'])
        self._validate_data_params(validated_config['data_params'])
        
        return validated_config
    
    def _validate_model_params(self, params: Dict[str, Any]):
        """验证模型参数"""
        # 隐藏层大小
        if params['hidden_size'] <= 0:
            params['hidden_size'] = 128
        
        # 层数
        if params['num_layers'] <= 0:
            params['num_layers'] = 2
        
        # Dropout率
        if not (0 <= params['dropout'] <= 1):
            params['dropout'] = 0.2
        
        # 模型类型
        if params['model_type'] not in ['gru', 'transformer_gru']:
            params['model_type'] = 'gru'
        
        # 损失函数类型
        valid_loss_types = ['mse', 'huber', 'wmse', 'zero_aware', 'adaptive']
        if params['loss_type'] not in valid_loss_types:
            params['loss_type'] = 'mse'
    
    def _validate_training_params(self, params: Dict[str, Any]):
        """验证训练参数"""
        # 训练轮数
        if params['num_epochs'] <= 0:
            params['num_epochs'] = 50
        
        # 批次大小
        if params['batch_size'] <= 0:
            params['batch_size'] = 32
        
        # 学习率
        if params['learning_rate'] <= 0:
            params['learning_rate'] = 0.001
        
        # 权重衰减
        if params['weight_decay'] < 0:
            params['weight_decay'] = 1e-4
        
        # 早停耐心值
        if params['patience'] <= 0:
            params['patience'] = 10
    
    def _validate_data_params(self, params: Dict[str, Any]):
        """验证数据参数"""
        # 序列长度
        if params['sequence_length'] <= 0:
            params['sequence_length'] = 24
        
        # 数据分割比例
        total_ratio = params['train_ratio'] + params['val_ratio'] + params['test_ratio']
        if abs(total_ratio - 1.0) > 0.01:  # 允许小的浮点误差
            # 重新归一化
            params['train_ratio'] = 0.7
            params['val_ratio'] = 0.1
            params['test_ratio'] = 0.2
    
    def get_config_section(self, section: str) -> Dict[str, Any]:
        """获取配置的特定节"""
        return deepcopy(self.current_config.get(section, {}))
    
    def update_config_section(self, section: str, new_params: Dict[str, Any]):
        """更新配置的特定节"""
        if section in self.current_config:
            self.current_config[section].update(new_params)
        else:
            self.current_config[section] = new_params
    
    def get_config_history(self) -> list:
        """获取配置历史"""
        return self.config_history
    
    def export_config_template(self, file_path: str):
        """导出配置模板"""
        template = {
            'description': '电动汽车充电负荷预测系统配置模板',
            'version': '1.0',
            'config': self.default_config
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def create_config_backup(self) -> str:
        """创建配置备份"""
        timestamp = self._get_timestamp().replace(':', '-').replace(' ', '_')
        backup_path = f"configs/backup/config_backup_{timestamp}.json"
        
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        self.save_config(self.current_config, backup_path)
        
        return backup_path
