import sys
import io
import threading
import time
from typing import List, Callable, Optional
from contextlib import contextmanager


class LogManager:
    """统一日志管理器 - 确保GUI和终端日志同步"""
    
    def __init__(self):
        self.log_callbacks: List[Callable[[str], None]] = []
        self.lock = threading.Lock()
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        self.is_redirected = False
        
    def add_log_callback(self, callback: Callable[[str], None]):
        """添加日志回调函数"""
        with self.lock:
            if callback not in self.log_callbacks:
                self.log_callbacks.append(callback)
    
    def remove_log_callback(self, callback: Callable[[str], None]):
        """移除日志回调函数"""
        with self.lock:
            if callback in self.log_callbacks:
                self.log_callbacks.remove(callback)

    def clear_log_callbacks(self):
        """清除所有日志回调函数"""
        with self.lock:
            self.log_callbacks.clear()
    
    def log(self, message: str, level: str = "INFO"):
        """统一日志输出 - 增强版实时同步"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # 直接输出到终端，避免递归
        self.original_stdout.write(formatted_message + "\n")
        self.original_stdout.flush()

        # 异步通知所有回调以提高性能
        self._notify_callbacks_async(message, level)
    
    def _notify_callbacks_async(self, message: str, level: str):
        """异步通知回调，提高GUI响应性"""
        import threading
        
        def notify_callbacks():
            with self.lock:
                active_callbacks = len(self.log_callbacks)
                
                for i, callback in enumerate(self.log_callbacks):
                    try:
                        # 根据回调类型决定是否需要线程安全调用
                        if hasattr(callback, '__self__') and hasattr(callback.__self__, 'frame'):
                            # 这是GUI组件的方法，使用after方法确保线程安全
                            if hasattr(callback.__self__.frame, 'after'):
                                callback.__self__.frame.after(0, lambda: callback(message))
                            else:
                                callback(message)
                        else:
                            # 普通回调直接调用
                            callback(message)
                            
                    except Exception as e:
                        # 避免回调错误影响日志系统
                        self.original_stdout.write(f"❌ 日志回调 {i+1}/{active_callbacks} 错误: {e}\n")
                        self.original_stdout.flush()
        
        # 在新线程中执行回调通知，避免阻塞主线程
        if self.log_callbacks:
            threading.Thread(target=notify_callbacks, daemon=True).start()
    
    @contextmanager
    def redirect_stdout(self):
        """临时重定向标准输出到日志系统"""
        if self.is_redirected:
            yield
            return
            
        class LogCapture(io.StringIO):
            def __init__(self, log_manager):
                super().__init__()
                self.log_manager = log_manager
                self.buffer = ""
            
            def write(self, text):
                if text and text.strip():
                    # 处理多行输出
                    lines = text.split('\n')
                    for line in lines:
                        if line.strip():
                            self.log_manager.log(line.strip())
                return len(text)
            
            def flush(self):
                pass
        
        try:
            self.is_redirected = True
            log_capture = LogCapture(self)
            sys.stdout = log_capture
            yield
        finally:
            sys.stdout = self.original_stdout
            self.is_redirected = False


class TqdmToLog:
    """将tqdm输出重定向到日志系统"""
    
    def __init__(self, log_manager: LogManager):
        self.log_manager = log_manager
        self.last_message = ""
    
    def write(self, text):
        if text and text.strip() and text.strip() != self.last_message:
            # 清理tqdm的特殊字符
            clean_text = text.replace('\r', '').replace('\n', '').strip()
            if clean_text and not clean_text.startswith('\x1b'):  # 忽略ANSI转义序列
                self.log_manager.log(clean_text)
                self.last_message = clean_text
    
    def flush(self):
        pass


# 全局日志管理器实例
_log_manager = None


def get_log_manager() -> LogManager:
    """获取全局日志管理器实例"""
    global _log_manager
    if _log_manager is None:
        _log_manager = LogManager()
    return _log_manager


def setup_training_logging(training_panel_callback=None, status_panel_callback=None):
    """设置训练日志系统"""
    log_manager = get_log_manager()

    # 清除现有回调
    log_manager.log_callbacks.clear()

    # 添加GUI回调
    callback_count = 0
    if training_panel_callback:
        log_manager.add_log_callback(training_panel_callback)
        callback_count += 1
        print(f"✅ 训练面板日志回调已注册")

    if status_panel_callback:
        log_manager.add_log_callback(status_panel_callback)
        callback_count += 1
        print(f"✅ 状态面板日志回调已注册")

    print(f"📊 日志系统初始化完成，共注册 {callback_count} 个回调")

    return log_manager


def log_message(message: str, level: str = "INFO"):
    """便捷的日志输出函数"""
    log_manager = get_log_manager()
    log_manager.log(message, level)


def create_tqdm_redirect():
    """创建tqdm重定向对象"""
    log_manager = get_log_manager()
    return TqdmToLog(log_manager)


# 用于替换print的函数
def log_print(*args, **kwargs):
    """替换print函数，确保输出被日志系统捕获"""
    message = ' '.join(str(arg) for arg in args)
    # 直接使用原始stdout，避免递归
    _log_manager.original_stdout.write(message + "\n")
    _log_manager.original_stdout.flush()
    # 通知回调
    with _log_manager.lock:
        for callback in _log_manager.log_callbacks:
            try:
                callback(message)
            except Exception:
                pass  # 忽略回调错误


# 上下文管理器，用于训练期间的日志重定向
@contextmanager
def training_log_context(training_callback=None, status_callback=None):
    """训练日志上下文管理器"""
    log_manager = setup_training_logging(training_callback, status_callback)

    # 备份原始print函数
    original_print = print

    try:
        # 重定向print到日志系统
        import builtins
        builtins.print = log_print

        yield log_manager
    finally:
        # 恢复原始print函数
        builtins.print = original_print
