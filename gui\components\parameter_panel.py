"""
参数设置面板 - 提供模型和训练参数的配置界面
Parameter Panel - Provides configuration interface for model and training parameters
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any


class ParameterPanel:
    """参数设置面板类"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)
        self.config_vars = {}
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主容器
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分栏
        left_frame = ttk.Frame(main_container)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        right_frame = ttk.Frame(main_container)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 左侧：参数分类树
        self.create_parameter_tree(left_frame)
        
        # 右侧：参数详细设置
        self.create_parameter_details(right_frame)
        
        # 底部：操作按钮
        self.create_action_buttons(main_container)
        
    def create_parameter_tree(self, parent):
        """创建参数分类树"""
        tree_frame = ttk.LabelFrame(parent, text="参数分类", padding=10)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建树形控件
        self.param_tree = ttk.Treeview(tree_frame, selectmode='browse')
        self.param_tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        tree_scroll = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.param_tree.yview)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.param_tree.configure(yscrollcommand=tree_scroll.set)
        
        # 构建参数树
        self.build_parameter_tree()
        
        # 绑定选择事件
        self.param_tree.bind('<<TreeviewSelect>>', self.on_tree_select)
        
    def build_parameter_tree(self):
        """构建参数树结构"""
        # 清空现有项目
        for item in self.param_tree.get_children():
            self.param_tree.delete(item)
        
        # 添加参数分类
        categories = [
            ("model_params", "🤖 模型参数"),
            ("training_params", "🎯 训练参数"),
            ("data_params", "📊 数据参数"),
            ("ssa_params", "🔍 SSA优化参数"),
            ("vmd_params", "🌊 VMD分解参数"),
            ("visualization_params", "📈 可视化参数"),
            ("system_params", "⚙️ 系统参数"),
            ("gui_params", "🖥️ 界面参数")
        ]
        
        for category_id, category_name in categories:
            self.param_tree.insert('', 'end', iid=category_id, text=category_name, open=True)
        
        # 默认选择第一个分类
        if categories:
            self.param_tree.selection_set(categories[0][0])
            self.current_category = categories[0][0]
            
    def create_parameter_details(self, parent):
        """创建参数详细设置区域"""
        details_frame = ttk.LabelFrame(parent, text="参数设置", padding=10)
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建滚动区域
        canvas = tk.Canvas(details_frame)
        scrollbar = ttk.Scrollbar(details_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.details_canvas = canvas
        
        # 初始化当前分类
        self.current_category = "model_params"
        self.update_parameter_details()
        
    def create_action_buttons(self, parent):
        """创建操作按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 左侧按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="🔄 重置为默认", 
                  command=self.reset_to_default).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="📋 复制配置", 
                  command=self.copy_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="📄 粘贴配置", 
                  command=self.paste_config).pack(side=tk.LEFT, padx=(0, 5))
        
        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(right_buttons, text="💾 保存配置", 
                  command=self.save_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_buttons, text="📁 加载配置", 
                  command=self.load_config_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_buttons, text="✅ 应用设置", 
                  command=self.apply_config, 
                  style="Accent.TButton").pack(side=tk.LEFT)
        
    def on_tree_select(self, event):
        """处理树形控件选择事件"""
        selection = self.param_tree.selection()
        if selection:
            self.current_category = selection[0]
            self.update_parameter_details()
            
    def update_parameter_details(self):
        """更新参数详细设置区域"""
        # 清空现有控件
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # 获取当前分类的配置
        config = self.main_app.config_manager.get_config_section(self.current_category)
        
        if not config:
            ttk.Label(self.scrollable_frame, text="该分类暂无参数").pack(pady=20)
            return
        
        # 创建参数控件
        self.create_parameter_widgets(config)
        
    def create_parameter_widgets(self, config: Dict[str, Any]):
        """创建参数控件"""
        row = 0
        
        for param_name, param_value in config.items():
            # 创建参数标签
            label_text = self.get_parameter_label(param_name)
            ttk.Label(self.scrollable_frame, text=label_text).grid(
                row=row, column=0, sticky='w', padx=(0, 10), pady=5
            )
            
            # 创建参数输入控件
            widget = self.create_parameter_widget(param_name, param_value)
            widget.grid(row=row, column=1, sticky='ew', pady=5)
            
            # 创建帮助按钮
            help_btn = ttk.Button(self.scrollable_frame, text="?", width=3,
                                 command=lambda p=param_name: self.show_parameter_help(p))
            help_btn.grid(row=row, column=2, padx=(5, 0), pady=5)
            
            row += 1
        
        # 配置列权重
        self.scrollable_frame.columnconfigure(1, weight=1)
        
    def create_parameter_widget(self, param_name: str, param_value: Any) -> tk.Widget:
        """根据参数类型创建相应的输入控件"""
        var_name = f"{self.current_category}_{param_name}"
        
        if isinstance(param_value, bool):
            # 布尔值 - 复选框
            var = tk.BooleanVar(master=self.scrollable_frame, value=param_value)
            widget = ttk.Checkbutton(self.scrollable_frame, variable=var)

        elif isinstance(param_value, int):
            # 整数 - 数字输入框
            var = tk.IntVar(master=self.scrollable_frame, value=param_value)
            widget = ttk.Spinbox(self.scrollable_frame, from_=0, to=10000, textvariable=var)

        elif isinstance(param_value, float):
            # 浮点数 - 数字输入框
            var = tk.DoubleVar(master=self.scrollable_frame, value=param_value)
            widget = ttk.Entry(self.scrollable_frame, textvariable=var)
            
        elif isinstance(param_value, str):
            # 字符串 - 下拉框或文本框
            if param_name in self.get_choice_parameters():
                var = tk.StringVar(master=self.scrollable_frame, value=param_value)
                choices = self.get_parameter_choices(param_name)
                widget = ttk.Combobox(self.scrollable_frame, textvariable=var,
                                    values=choices, state='readonly')
            else:
                var = tk.StringVar(master=self.scrollable_frame, value=param_value)
                widget = ttk.Entry(self.scrollable_frame, textvariable=var)
        else:
            # 其他类型 - 文本框
            var = tk.StringVar(master=self.scrollable_frame, value=str(param_value))
            widget = ttk.Entry(self.scrollable_frame, textvariable=var)
        
        # 保存变量引用
        self.config_vars[var_name] = var
        
        return widget
    
    def get_parameter_label(self, param_name: str) -> str:
        """获取参数的中文标签"""
        label_map = {
            # 模型参数
            'hidden_size': '隐藏层大小',
            'num_layers': '网络层数',
            'dropout': 'Dropout率',
            'model_type': '模型类型',
            'loss_type': '损失函数',
            'alpha': '权重系数',
            
            # 训练参数
            'num_epochs': '训练轮数',
            'batch_size': '批次大小',
            'learning_rate': '学习率',
            'weight_decay': '权重衰减',
            'patience': '早停耐心值',
            'validation_split': '验证集比例',
            
            # 数据参数
            'sequence_length': '序列长度',
            'train_ratio': '训练集比例',
            'val_ratio': '验证集比例',
            'test_ratio': '测试集比例',
            'normalize_features': '特征归一化',
            'handle_missing': '处理缺失值',
            'remove_outliers': '移除异常值',
            
            # SSA参数
            'use_ssa_optimization': '启用SSA优化',
            'pop_size': '种群大小',
            'max_iter': '最大迭代数',
            'use_parallel': '并行计算',
            'n_workers': '工作进程数',
            
            # VMD参数
            'use_vmd': '启用VMD分解',
            'K': '模态数量',
            'tau': '噪声容忍度',
            'DC': '直流分量',
            'init': '初始化方法',
            'tol': '收敛容忍度',
            
            # 可视化参数
            'figure_size': '图表大小',
            'dpi': '图像分辨率',
            'style': '绘图样式',
            'color_palette': '颜色方案',
            'show_grid': '显示网格',
            'save_plots': '保存图表',
            
            # 系统参数
            'use_gpu': '使用GPU',
            'gpu_memory_fraction': 'GPU内存比例',
            'random_seed': '随机种子',
            'log_level': '日志级别',
            'save_checkpoints': '保存检查点',
            'checkpoint_interval': '检查点间隔',
            
            # 界面参数
            'theme': '界面主题',
            'font_size': '字体大小',
            'auto_refresh': '自动刷新',
            'refresh_interval': '刷新间隔',
            'show_tooltips': '显示提示',
            'remember_window_size': '记住窗口大小'
        }
        
        return label_map.get(param_name, param_name)
    
    def get_choice_parameters(self) -> list:
        """获取需要下拉选择的参数列表"""
        return [
            'model_type', 'loss_type', 'style', 'color_palette', 
            'theme', 'log_level', 'init'
        ]
    
    def get_parameter_choices(self, param_name: str) -> list:
        """获取参数的选择项"""
        choices_map = {
            'model_type': ['gru', 'transformer_gru'],
            'loss_type': ['mse', 'huber', 'wmse', 'zero_aware', 'adaptive'],
            'style': ['default', 'seaborn-v0_8', 'ggplot', 'bmh'],
            'color_palette': ['viridis', 'plasma', 'inferno', 'magma', 'tab10'],
            'theme': ['default', 'clam', 'alt', 'classic'],
            'log_level': ['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            'init': ['0', '1', '2']
        }
        
        return choices_map.get(param_name, [])
    
    def show_parameter_help(self, param_name: str):
        """显示参数帮助信息"""
        help_map = {
            'hidden_size': '神经网络隐藏层的神经元数量，影响模型复杂度',
            'num_layers': 'GRU网络的层数，更多层可以学习更复杂的模式',
            'dropout': '防止过拟合的正则化技术，范围0-1',
            'model_type': '选择使用的模型架构类型',
            'loss_type': '训练时使用的损失函数类型',
            'learning_rate': '优化器的学习步长，影响训练速度和稳定性',
            'batch_size': '每次训练使用的样本数量',
            'sequence_length': '输入序列的时间步长度',
            'use_ssa_optimization': '是否使用麻雀搜索算法优化超参数',
            'pop_size': 'SSA算法的种群大小',
            'use_vmd': '是否使用变分模态分解预处理数据'
        }
        
        help_text = help_map.get(param_name, f"参数 {param_name} 的帮助信息")
        messagebox.showinfo("参数帮助", help_text)
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        config = {}
        
        for var_name, var in self.config_vars.items():
            category, param_name = var_name.split('_', 1)
            
            if category not in config:
                config[category] = {}
            
            config[category][param_name] = var.get()
        
        return config
    
    def load_config(self, config: Dict[str, Any]):
        """加载配置到界面"""
        for category, params in config.items():
            for param_name, param_value in params.items():
                var_name = f"{category}_{param_name}"
                if var_name in self.config_vars:
                    try:
                        self.config_vars[var_name].set(param_value)
                    except:
                        pass  # 忽略类型不匹配的错误
        
        # 刷新当前显示
        self.update_parameter_details()
    
    def reset_to_default(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认", "确定要重置当前分类的参数为默认值吗？"):
            default_config = self.main_app.config_manager.default_config
            current_defaults = default_config.get(self.current_category, {})
            
            for param_name, param_value in current_defaults.items():
                var_name = f"{self.current_category}_{param_name}"
                if var_name in self.config_vars:
                    try:
                        self.config_vars[var_name].set(param_value)
                    except:
                        pass
    
    def copy_config(self):
        """复制当前配置到剪贴板"""
        config = self.get_config()
        import json
        config_json = json.dumps(config, indent=2, ensure_ascii=False)
        
        self.frame.clipboard_clear()
        self.frame.clipboard_append(config_json)
        messagebox.showinfo("成功", "配置已复制到剪贴板")
    
    def paste_config(self):
        """从剪贴板粘贴配置"""
        try:
            import json
            config_json = self.frame.clipboard_get()
            config = json.loads(config_json)
            self.load_config(config)
            messagebox.showinfo("成功", "配置已从剪贴板加载")
        except Exception as e:
            messagebox.showerror("错误", f"粘贴配置失败: {str(e)}")
    
    def save_config(self):
        """保存配置"""
        self.main_app.save_project()
    
    def load_config_file(self):
        """加载配置文件"""
        self.main_app.open_project()
    
    def apply_config(self):
        """应用配置"""
        try:
            config = self.get_config()
            self.main_app.config_manager.update_config(config)
            messagebox.showinfo("成功", "配置已应用")
        except Exception as e:
            messagebox.showerror("错误", f"应用配置失败: {str(e)}")

    def get_frame(self):
        """获取面板框架"""
        return self.frame
