"""
预测面板 - 提供模型预测和结果展示功能
Prediction Panel - Provides model prediction and result display functionality
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import pandas as pd
import threading
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from gui.managers.results_manager import get_results_manager


class PredictionPanel:
    """预测面板类"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)
        self.prediction_results = None
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：预测控制
        left_frame = ttk.Frame(main_container)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧：结果展示
        right_frame = ttk.Frame(main_container)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_control_section(left_frame)
        self.create_results_section(right_frame)
        
    def create_control_section(self, parent):
        """创建预测控制区域"""
        # 模型选择
        model_frame = ttk.LabelFrame(parent, text="🤖 模型选择", padding=10)
        model_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.model_var = tk.StringVar(value="当前训练模型")
        ttk.Radiobutton(model_frame, text="当前训练模型", 
                       variable=self.model_var, value="current").pack(anchor=tk.W)
        ttk.Radiobutton(model_frame, text="加载已保存模型", 
                       variable=self.model_var, value="saved").pack(anchor=tk.W)
        
        # 模型文件选择
        file_frame = ttk.Frame(model_frame)
        file_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.model_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.model_path_var, width=25).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="浏览", command=self.browse_model).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 预测数据选择
        data_frame = ttk.LabelFrame(parent, text="📊 预测数据", padding=10)
        data_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.data_source_var = tk.StringVar(value="test")
        ttk.Radiobutton(data_frame, text="使用测试集", 
                       variable=self.data_source_var, value="test").pack(anchor=tk.W)
        ttk.Radiobutton(data_frame, text="上传新数据", 
                       variable=self.data_source_var, value="upload").pack(anchor=tk.W)
        
        # 新数据文件选择
        upload_frame = ttk.Frame(data_frame)
        upload_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.data_path_var = tk.StringVar()
        ttk.Entry(upload_frame, textvariable=self.data_path_var, width=25).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(upload_frame, text="浏览", command=self.browse_data).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 预测控制
        control_frame = ttk.LabelFrame(parent, text="⚙️ 预测控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.predict_button = ttk.Button(control_frame, text="🔮 开始预测", 
                                        command=self.start_prediction,
                                        style="Accent.TButton")
        self.predict_button.pack(fill=tk.X, pady=(0, 5))
        
        # 预测进度
        self.predict_progress_var = tk.DoubleVar()
        self.predict_progress = ttk.Progressbar(control_frame, 
                                              variable=self.predict_progress_var,
                                              maximum=100)
        self.predict_progress.pack(fill=tk.X, pady=(0, 5))
        
        self.predict_status_var = tk.StringVar(value="等待开始预测...")
        ttk.Label(control_frame, textvariable=self.predict_status_var).pack()
        
        # 结果操作
        result_frame = ttk.LabelFrame(parent, text="💾 结果操作", padding=10)
        result_frame.pack(fill=tk.X)
        
        ttk.Button(result_frame, text="📊 查看详细结果", 
                  command=self.show_detailed_results).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(result_frame, text="💾 导出结果", 
                  command=self.export_results).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(result_frame, text="📈 生成报告", 
                  command=self.generate_report).pack(fill=tk.X)
        
    def create_results_section(self, parent):
        """创建结果展示区域"""
        # 创建笔记本控件
        self.results_notebook = ttk.Notebook(parent)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 预测图表标签页
        self.create_prediction_plot_tab()
        
        # 结果表格标签页
        self.create_results_table_tab()
        
        # 性能指标标签页
        self.create_metrics_tab()
        
    def create_prediction_plot_tab(self):
        """创建预测图表标签页"""
        plot_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(plot_tab, text="📈 预测图表")

        # 创建matplotlib图表
        self.pred_fig, self.pred_ax = plt.subplots(figsize=(10, 6))

        # 确保字体配置
        from gui.utils.gui_utils import get_font_manager
        font_manager = get_font_manager()

        # 设置中文字体属性
        font_prop = {'family': font_manager.matplotlib_font, 'size': 12}
        label_font_prop = {'family': font_manager.matplotlib_font, 'size': 10}

        # 设置标题和坐标轴标签，明确指定字体
        self.pred_ax.set_title('预测结果对比', fontdict=font_prop)
        self.pred_ax.set_xlabel('时间', fontdict=label_font_prop)
        self.pred_ax.set_ylabel('充电负荷 (kW)', fontdict=label_font_prop)
        self.pred_ax.grid(True, alpha=0.3)
        
        # 嵌入到tkinter
        self.pred_canvas = FigureCanvasTkAgg(self.pred_fig, plot_tab)
        self.pred_canvas.draw()
        self.pred_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 图表控制
        plot_controls = ttk.Frame(plot_tab)
        plot_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(plot_controls, text="🔄 刷新图表", 
                  command=self.refresh_prediction_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(plot_controls, text="💾 保存图表", 
                  command=self.save_prediction_plot).pack(side=tk.LEFT)
        
    def create_results_table_tab(self):
        """创建结果表格标签页"""
        table_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(table_tab, text="📋 结果表格")
        
        # 创建表格
        columns = ['时间', '真实值', '预测值', '误差', '相对误差(%)']
        self.results_tree = ttk.Treeview(table_tab, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120)
        
        # 添加滚动条
        table_scroll_v = ttk.Scrollbar(table_tab, orient=tk.VERTICAL, command=self.results_tree.yview)
        table_scroll_h = ttk.Scrollbar(table_tab, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        
        self.results_tree.configure(yscrollcommand=table_scroll_v.set, xscrollcommand=table_scroll_h.set)
        
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        table_scroll_v.pack(side=tk.RIGHT, fill=tk.Y)
        table_scroll_h.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_metrics_tab(self):
        """创建性能指标标签页"""
        metrics_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(metrics_tab, text="📊 性能指标")
        
        # 指标显示区域
        metrics_frame = ttk.LabelFrame(metrics_tab, text="预测性能指标", padding=20)
        metrics_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # 创建指标网格
        metrics_grid = ttk.Frame(metrics_frame)
        metrics_grid.pack(fill=tk.X)
        
        # MSE
        ttk.Label(metrics_grid, text="均方误差 (MSE):", font=("Arial", 12)).grid(row=0, column=0, sticky='w', padx=(0, 20))
        self.mse_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.mse_var, font=("Arial", 12, "bold")).grid(row=0, column=1, sticky='w')
        
        # RMSE
        ttk.Label(metrics_grid, text="均方根误差 (RMSE):", font=("Arial", 12)).grid(row=1, column=0, sticky='w', padx=(0, 20))
        self.rmse_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.rmse_var, font=("Arial", 12, "bold")).grid(row=1, column=1, sticky='w')
        
        # MAE
        ttk.Label(metrics_grid, text="平均绝对误差 (MAE):", font=("Arial", 12)).grid(row=2, column=0, sticky='w', padx=(0, 20))
        self.mae_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.mae_var, font=("Arial", 12, "bold")).grid(row=2, column=1, sticky='w')
        
        # R²
        ttk.Label(metrics_grid, text="决定系数 (R²):", font=("Arial", 12)).grid(row=0, column=2, sticky='w', padx=(40, 20))
        self.r2_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.r2_var, font=("Arial", 12, "bold")).grid(row=0, column=3, sticky='w')
        
        # MAPE
        ttk.Label(metrics_grid, text="平均绝对百分比误差 (MAPE):", font=("Arial", 12)).grid(row=1, column=2, sticky='w', padx=(40, 20))
        self.mape_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.mape_var, font=("Arial", 12, "bold")).grid(row=1, column=3, sticky='w')
        
        # 预测准确率
        ttk.Label(metrics_grid, text="预测准确率:", font=("Arial", 12)).grid(row=2, column=2, sticky='w', padx=(40, 20))
        self.accuracy_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.accuracy_var, font=("Arial", 12, "bold")).grid(row=2, column=3, sticky='w')
        
        # 配置网格权重
        for i in range(4):
            metrics_grid.columnconfigure(i, weight=1)
            
    def browse_model(self):
        """浏览模型文件"""
        file_path = filedialog.askopenfilename(
            title="选择模型文件",
            filetypes=[
                ("PyTorch模型", "*.pth"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.model_path_var.set(file_path)
            
    def browse_data(self):
        """浏览数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择预测数据文件",
            filetypes=[
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx;*.xls"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.data_path_var.set(file_path)
            
    def start_prediction(self):
        """开始预测"""
        # 检查模型是否可用
        if self.model_var.get() == "current":
            if not self.main_app.model_manager.is_model_available():
                messagebox.showerror("错误", "当前没有可用的训练模型")
                return
        else:
            model_path = self.model_path_var.get().strip()
            if not model_path:
                messagebox.showerror("错误", "请选择模型文件")
                return
        
        # 检查数据是否可用
        if self.data_source_var.get() == "test":
            if not self.main_app.data_manager.has_processed_data():
                messagebox.showerror("错误", "没有可用的测试数据")
                return
        else:
            data_path = self.data_path_var.get().strip()
            if not data_path:
                messagebox.showerror("错误", "请选择预测数据文件")
                return
        
        # 在后台线程中进行预测
        def prediction_thread():
            try:
                self.predict_status_var.set("正在进行预测...")
                self.predict_progress_var.set(50)
                
                # 模拟预测过程
                import time
                time.sleep(2)  # 模拟预测时间
                
                # 生成模拟预测结果
                self.generate_mock_results()
                
                self.predict_progress_var.set(100)
                self.predict_status_var.set("预测完成")
                
                # 在主线程中更新界面
                self.frame.after(0, self.on_prediction_completed)
                
            except Exception as e:
                self.frame.after(0, lambda: self.on_prediction_error(str(e)))
        
        threading.Thread(target=prediction_thread, daemon=True).start()
        
    def generate_mock_results(self):
        """生成模拟预测结果"""
        # 生成模拟数据
        n_samples = 100
        time_index = pd.date_range('2024-01-01', periods=n_samples, freq='H')
        
        # 模拟真实值和预测值
        np.random.seed(42)
        true_values = 50 + 20 * np.sin(np.arange(n_samples) * 2 * np.pi / 24) + np.random.normal(0, 5, n_samples)
        pred_values = true_values + np.random.normal(0, 3, n_samples)
        
        self.prediction_results = {
            'time': time_index,
            'true_values': true_values,
            'pred_values': pred_values,
            'errors': pred_values - true_values
        }
        
    def on_prediction_completed(self):
        """预测完成处理"""
        self.update_prediction_plot()
        self.update_results_table()
        self.update_metrics()
        messagebox.showinfo("成功", "预测完成")
        
    def on_prediction_error(self, error_message: str):
        """预测错误处理"""
        self.predict_status_var.set("预测失败")
        messagebox.showerror("错误", f"预测失败: {error_message}")
        
    def update_prediction_plot(self):
        """更新预测图表"""
        if self.prediction_results is None:
            return

        self.pred_ax.clear()

        # 确保字体配置
        from gui.utils.gui_utils import get_font_manager
        font_manager = get_font_manager()

        # 设置中文字体属性
        font_prop = {'family': font_manager.matplotlib_font, 'size': 12}
        label_font_prop = {'family': font_manager.matplotlib_font, 'size': 10}
        legend_font_prop = {'family': font_manager.matplotlib_font, 'size': 9}

        time_data = self.prediction_results['time']
        true_values = self.prediction_results['true_values']
        pred_values = self.prediction_results['pred_values']

        self.pred_ax.plot(time_data, true_values, label='真实值', linewidth=2, alpha=0.8)
        self.pred_ax.plot(time_data, pred_values, label='预测值', linewidth=2, alpha=0.8)

        # 设置标题和坐标轴标签，明确指定字体
        self.pred_ax.set_title('预测结果对比', fontdict=font_prop)
        self.pred_ax.set_xlabel('时间', fontdict=label_font_prop)
        self.pred_ax.set_ylabel('充电负荷 (kW)', fontdict=label_font_prop)
        self.pred_ax.legend(prop=legend_font_prop)
        self.pred_ax.grid(True, alpha=0.3)

        self.pred_canvas.draw()
        
    def update_results_table(self):
        """更新结果表格"""
        if self.prediction_results is None:
            return
        
        # 清空现有数据
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        time_data = self.prediction_results['time']
        true_values = self.prediction_results['true_values']
        pred_values = self.prediction_results['pred_values']
        errors = self.prediction_results['errors']
        
        for i in range(len(time_data)):
            time_str = time_data[i].strftime('%Y-%m-%d %H:%M')
            true_val = f"{true_values[i]:.2f}"
            pred_val = f"{pred_values[i]:.2f}"
            error = f"{errors[i]:.2f}"
            rel_error = f"{abs(errors[i]) / abs(true_values[i]) * 100:.2f}" if true_values[i] != 0 else "N/A"
            
            self.results_tree.insert('', 'end', values=(time_str, true_val, pred_val, error, rel_error))
            
    def update_metrics(self):
        """更新性能指标"""
        if self.prediction_results is None:
            return
        
        true_values = self.prediction_results['true_values']
        pred_values = self.prediction_results['pred_values']
        
        # 计算指标
        from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
        
        mse = mean_squared_error(true_values, pred_values)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(true_values, pred_values)
        r2 = r2_score(true_values, pred_values)
        
        # MAPE
        mape = np.mean(np.abs((true_values - pred_values) / true_values)) * 100
        
        # 预测准确率（基于相对误差小于10%的比例）
        relative_errors = np.abs((true_values - pred_values) / true_values) * 100
        accuracy = np.mean(relative_errors < 10) * 100
        
        # 更新界面
        self.mse_var.set(f"{mse:.4f}")
        self.rmse_var.set(f"{rmse:.4f}")
        self.mae_var.set(f"{mae:.4f}")
        self.r2_var.set(f"{r2:.4f}")
        self.mape_var.set(f"{mape:.2f}%")
        self.accuracy_var.set(f"{accuracy:.2f}%")
        
    def refresh_prediction_plot(self):
        """刷新预测图表"""
        self.update_prediction_plot()
        
    def save_prediction_plot(self):
        """保存预测图表"""
        try:
            # 使用结果管理器自动保存到当前会话目录
            results_manager = get_results_manager()
            plot_path = results_manager.save_figure(self.pred_fig, "prediction_results")
            messagebox.showinfo("成功", f"预测结果图表已保存到: {plot_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

    def save_prediction_plot_manual(self):
        """手动选择路径保存预测图表"""
        file_path = filedialog.asksaveasfilename(
            title="保存预测图表",
            defaultextension=".png",
            filetypes=[
                ("PNG文件", "*.png"),
                ("PDF文件", "*.pdf"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.pred_fig.savefig(file_path, dpi=300, bbox_inches='tight')
            messagebox.showinfo("成功", f"图表已保存到: {file_path}")
            
    def show_detailed_results(self):
        """显示详细结果"""
        if self.prediction_results is None:
            messagebox.showwarning("提示", "暂无预测结果")
            return
        
        # 切换到结果表格标签页
        self.results_notebook.select(1)
        
    def export_results(self):
        """导出预测结果"""
        if self.prediction_results is None:
            messagebox.showwarning("提示", "暂无预测结果")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="导出预测结果",
            defaultextension=".csv",
            filetypes=[
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                # 创建结果DataFrame
                results_df = pd.DataFrame({
                    '时间': self.prediction_results['time'],
                    '真实值': self.prediction_results['true_values'],
                    '预测值': self.prediction_results['pred_values'],
                    '误差': self.prediction_results['errors']
                })
                
                if file_path.endswith('.xlsx'):
                    results_df.to_excel(file_path, index=False)
                else:
                    results_df.to_csv(file_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo("成功", f"结果已导出到: {file_path}")
                
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")
                
    def generate_report(self):
        """生成预测报告"""
        if self.prediction_results is None:
            messagebox.showwarning("提示", "暂无预测结果")
            return
        
        messagebox.showinfo("提示", "报告生成功能正在开发中...")
    
    def get_frame(self):
        """获取面板框架"""
        return self.frame
