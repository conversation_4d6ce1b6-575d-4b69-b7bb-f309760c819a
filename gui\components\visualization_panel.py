"""
可视化面板 - 提供数据可视化和图表展示功能
Visualization Panel - Provides data visualization and chart display functionality
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import pandas as pd

# 导入字体配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from gui.utils.gui_utils import setup_all_fonts, get_font_manager
from gui.managers.results_manager import get_results_manager

# 设置字体
setup_all_fonts()
import seaborn as sns


class VisualizationPanel:
    """可视化面板类"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)
        self.current_plot_type = "data_overview"

        # 获取字体管理器
        try:
            self.font_manager = get_font_manager()
        except:
            self.font_manager = None

        self.create_widgets()

    def _get_font_props(self):
        """获取字体属性"""
        if self.font_manager:
            return {
                'title': {'family': self.font_manager.matplotlib_font, 'size': 12},
                'label': {'family': self.font_manager.matplotlib_font, 'size': 10},
                'legend': {'family': self.font_manager.matplotlib_font, 'size': 9}
            }
        else:
            return {
                'title': {'family': 'Microsoft YaHei', 'size': 12},
                'label': {'family': 'Microsoft YaHei', 'size': 10},
                'legend': {'family': 'Microsoft YaHei', 'size': 9}
            }

    def _get_font(self, font_type="default"):
        """获取字体的辅助方法"""
        if self.font_manager:
            if font_type == "title":
                return self.font_manager.get_title_font()
            elif font_type == "label":
                return self.font_manager.get_label_font()
            elif font_type == "button":
                return self.font_manager.get_button_font()
            else:
                return self.font_manager.get_default_font()
        return None
        
    def create_widgets(self):
        """创建界面组件"""
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：图表控制
        left_frame = ttk.Frame(main_container)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧：图表显示
        right_frame = ttk.Frame(main_container)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_control_section(left_frame)
        self.create_visualization_section(right_frame)
        
    def create_control_section(self, parent):
        """创建图表控制区域"""
        # 图表类型选择
        chart_type_frame = ttk.LabelFrame(parent, text="📊 图表类型", padding=10)
        chart_type_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.chart_type_var = tk.StringVar(master=chart_type_frame, value="data_overview")
        
        chart_types = [
            ("数据概览", "data_overview"),
            ("时间序列", "time_series"),
            ("相关性分析", "correlation"),
            ("分布分析", "distribution"),
            ("预测对比", "prediction_comparison"),
            ("特征重要性", "feature_importance"),
            ("训练历史", "training_history"),
            ("性能指标", "performance_metrics")
        ]
        
        for text, value in chart_types:
            ttk.Radiobutton(chart_type_frame, text=text, 
                           variable=self.chart_type_var, value=value,
                           command=self.on_chart_type_changed).pack(anchor=tk.W, pady=2)
        
        # 图表参数设置
        params_frame = ttk.LabelFrame(parent, text="⚙️ 图表参数", padding=10)
        params_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 图表样式
        style_frame = ttk.Frame(params_frame)
        style_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(style_frame, text="样式:").pack(side=tk.LEFT)
        self.style_var = tk.StringVar(value="default")
        # 获取可用的matplotlib样式
        available_styles = ["default", "classic", "ggplot", "bmh"]
        try:
            # 检查seaborn是否可用
            import matplotlib.pyplot as plt
            if "seaborn" in plt.style.available or "seaborn-v0_8" in plt.style.available:
                available_styles.insert(1, "seaborn-v0_8")
        except:
            pass

        style_combo = ttk.Combobox(style_frame, textvariable=self.style_var,
                                  values=available_styles,
                                  state="readonly", width=15)
        style_combo.pack(side=tk.RIGHT)
        
        # 颜色主题
        color_frame = ttk.Frame(params_frame)
        color_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(color_frame, text="颜色:").pack(side=tk.LEFT)
        self.color_var = tk.StringVar(value="viridis")
        color_combo = ttk.Combobox(color_frame, textvariable=self.color_var,
                                  values=["viridis", "plasma", "inferno", "magma", "coolwarm"],
                                  state="readonly", width=15)
        color_combo.pack(side=tk.RIGHT)
        
        # 图表尺寸
        size_frame = ttk.Frame(params_frame)
        size_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(size_frame, text="尺寸:").pack(side=tk.LEFT)
        self.size_var = tk.StringVar(value="10x6")
        size_combo = ttk.Combobox(size_frame, textvariable=self.size_var,
                                 values=["8x6", "10x6", "12x8", "14x10"],
                                 state="readonly", width=15)
        size_combo.pack(side=tk.RIGHT)
        
        # 显示网格
        self.grid_var = tk.BooleanVar(master=params_frame, value=True)
        ttk.Checkbutton(params_frame, text="显示网格",
                       variable=self.grid_var).pack(anchor=tk.W, pady=2)

        # 显示图例
        self.legend_var = tk.BooleanVar(master=params_frame, value=True)
        ttk.Checkbutton(params_frame, text="显示图例",
                       variable=self.legend_var).pack(anchor=tk.W, pady=2)
        
        # 数据选择
        data_frame = ttk.LabelFrame(parent, text="📈 数据选择", padding=10)
        data_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 时间范围
        time_range_frame = ttk.Frame(data_frame)
        time_range_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(time_range_frame, text="时间范围:").pack(side=tk.LEFT)
        self.time_range_var = tk.StringVar(value="全部")
        time_combo = ttk.Combobox(time_range_frame, textvariable=self.time_range_var,
                                 values=["全部", "最近7天", "最近30天", "最近90天"],
                                 state="readonly", width=12)
        time_combo.pack(side=tk.RIGHT)
        
        # 数据采样
        sample_frame = ttk.Frame(data_frame)
        sample_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(sample_frame, text="采样率:").pack(side=tk.LEFT)
        self.sample_var = tk.StringVar(value="100%")
        sample_combo = ttk.Combobox(sample_frame, textvariable=self.sample_var,
                                   values=["100%", "50%", "25%", "10%"],
                                   state="readonly", width=12)
        sample_combo.pack(side=tk.RIGHT)
        
        # 控制按钮
        control_frame = ttk.LabelFrame(parent, text="🎮 控制", padding=10)
        control_frame.pack(fill=tk.X)
        
        ttk.Button(control_frame, text="🔄 刷新图表", 
                  command=self.refresh_chart,
                  style="Accent.TButton").pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(control_frame, text="💾 保存图表", 
                  command=self.save_chart).pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(control_frame, text="📋 复制数据", 
                  command=self.copy_data).pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(control_frame, text="📊 导出数据", 
                  command=self.export_data).pack(fill=tk.X)
        
    def create_visualization_section(self, parent):
        """创建可视化显示区域"""
        # 创建matplotlib图表
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 图表工具栏
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(5, 0))
        
        # 缩放控制
        zoom_frame = ttk.Frame(toolbar_frame)
        zoom_frame.pack(side=tk.LEFT)
        
        ttk.Button(zoom_frame, text="🔍+", command=self.zoom_in).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(zoom_frame, text="🔍-", command=self.zoom_out).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(zoom_frame, text="🏠", command=self.zoom_home).pack(side=tk.LEFT, padx=(0, 10))
        
        # 图表信息
        info_frame = ttk.Frame(toolbar_frame)
        info_frame.pack(side=tk.RIGHT)
        
        self.chart_info_var = tk.StringVar(value="就绪")
        ttk.Label(info_frame, textvariable=self.chart_info_var, 
                 font=("Arial", 9)).pack(side=tk.RIGHT)
        
        # 初始化图表
        self.update_chart()
        
    def on_chart_type_changed(self):
        """图表类型改变时的处理"""
        self.update_chart()
        
    def update_chart(self):
        """更新图表（线程安全）"""
        try:
            # 确保在主线程中执行
            if hasattr(self, 'frame') and self.frame.winfo_exists():
                self.frame.after(0, self._update_chart_safe)
            else:
                print("警告: 图表框架不存在或已销毁")
        except Exception as e:
            print(f"图表更新调度失败: {e}")
            # 尝试直接更新
            try:
                self._update_chart_safe()
            except Exception as direct_error:
                print(f"直接图表更新也失败: {direct_error}")

    def _update_chart_safe(self):
        """安全更新图表（在主线程中执行）"""
        chart_type = self.chart_type_var.get()

        try:
            # 检查必要的组件是否存在
            if not hasattr(self, 'ax') or self.ax is None:
                print("错误: 图表轴对象不存在")
                return

            if not hasattr(self, 'canvas') or self.canvas is None:
                print("错误: 画布对象不存在")
                return

            # 清空当前图表
            self.ax.clear()

            # 根据图表类型绘制不同的图表
            print(f"正在绘制图表类型: {chart_type}")

            if chart_type == "data_overview":
                self.plot_data_overview()
            elif chart_type == "time_series":
                self.plot_time_series()
            elif chart_type == "correlation":
                self.plot_correlation()
            elif chart_type == "distribution":
                self.plot_distribution()
            elif chart_type == "prediction_comparison":
                self.plot_prediction_comparison()
            elif chart_type == "feature_importance":
                self.plot_feature_importance()
            elif chart_type == "training_history":
                self.plot_training_history()
            elif chart_type == "performance_metrics":
                self.plot_performance_metrics()
            else:
                print(f"未知的图表类型: {chart_type}")
                self.ax.text(0.5, 0.5, f'未知图表类型\n{chart_type}',
                            ha='center', va='center', transform=self.ax.transAxes,
                            fontsize=16, alpha=0.5)

            # 应用样式设置
            self.apply_chart_style()

            # 刷新画布
            self.canvas.draw()

            self.chart_info_var.set(f"图表已更新: {chart_type}")
            print(f"图表更新成功: {chart_type}")

        except Exception as e:
            error_msg = f"图表更新失败: {str(e)}"
            print(error_msg)
            self.chart_info_var.set(error_msg)

            # 尝试显示错误信息在图表上
            try:
                if hasattr(self, 'ax') and self.ax is not None:
                    self.ax.clear()
                    self.ax.text(0.5, 0.5, f'图表更新失败\n{str(e)}',
                                ha='center', va='center', transform=self.ax.transAxes,
                                fontsize=14, alpha=0.7)
                    self.ax.set_title('错误', fontsize=16)
                    if hasattr(self, 'canvas') and self.canvas is not None:
                        self.canvas.draw()
            except Exception as display_error:
                print(f"显示错误信息也失败: {display_error}")

            # 只在GUI环境中显示消息框
            try:
                import tkinter as tk
                if tk._default_root is not None:
                    messagebox.showerror("错误", error_msg)
            except Exception as msg_error:
                print(f"显示错误消息框失败: {msg_error}")
            
    def plot_data_overview(self):
        """绘制数据概览图"""
        try:
            # 获取字体属性
            font_props = self._get_font_props()

            # 检查数据管理器是否存在
            if not hasattr(self.main_app, 'data_manager') or self.main_app.data_manager is None:
                self.ax.text(0.5, 0.5, '数据管理器未初始化',
                            ha='center', va='center', transform=self.ax.transAxes,
                            fontsize=16, alpha=0.5, fontfamily=font_props['title']['family'])
                self.ax.set_title('数据概览', fontdict=font_props['title'])
                return

            # 检查是否有数据
            if not self.main_app.data_manager.has_data():
                self.ax.text(0.5, 0.5, '暂无数据\n请先加载数据',
                            ha='center', va='center', transform=self.ax.transAxes,
                            fontsize=16, alpha=0.5, fontfamily=font_props['title']['family'])
                self.ax.set_title('数据概览', fontdict=font_props['title'])
                return

            # 检查数据是否有效
            if not self.main_app.data_manager.is_data_valid():
                self.ax.text(0.5, 0.5, '数据无效\n请检查数据格式',
                            ha='center', va='center', transform=self.ax.transAxes,
                            fontsize=16, alpha=0.5, fontfamily=font_props['title']['family'])
                self.ax.set_title('数据概览', fontdict=font_props['title'])
                return

            # 获取数据
            data = self.main_app.data_manager.get_data()

            if data is None:
                self.ax.text(0.5, 0.5, '数据获取失败',
                            ha='center', va='center', transform=self.ax.transAxes,
                            fontsize=16, alpha=0.5, fontfamily=font_props['title']['family'])
                self.ax.set_title('数据概览', fontdict=font_props['title'])
                return

            # 验证数据不为空
            if data.empty:
                self.ax.text(0.5, 0.5, '数据为空\n请检查数据文件',
                            ha='center', va='center', transform=self.ax.transAxes,
                            fontsize=16, alpha=0.5, fontfamily=font_props['title']['family'])
                self.ax.set_title('数据概览', fontdict=font_props['title'])
                return

            # 绘制主要特征的时间序列
            try:
                if '充电时间' in data.columns:
                    time_col = '充电时间'
                    if pd.api.types.is_datetime64_any_dtype(data[time_col]):
                        x = data[time_col]
                    else:
                        x = pd.to_datetime(data[time_col])
                else:
                    x = range(len(data))

                # 选择数值列进行绘制
                numeric_cols = data.select_dtypes(include=[np.number]).columns[:4]  # 最多4个特征

                if len(numeric_cols) == 0:
                    self.ax.text(0.5, 0.5, '没有数值列可显示\n请检查数据格式',
                                ha='center', va='center', transform=self.ax.transAxes,
                                fontsize=16, alpha=0.5, fontfamily=font_props['title']['family'])
                    self.ax.set_title('数据概览', fontdict=font_props['title'])
                    return

                # 绘制数值列
                for i, col in enumerate(numeric_cols):
                    try:
                        # 检查列是否有有效数据
                        col_data = data[col].dropna()
                        if len(col_data) == 0:
                            continue

                        self.ax.plot(x[:len(col_data)], col_data, label=col, alpha=0.7, linewidth=1.5)
                    except Exception as col_error:
                        print(f"绘制列 {col} 时出错: {col_error}")
                        continue

                self.ax.set_title('数据概览 - 主要特征时间序列', fontdict=font_props['title'])
                self.ax.set_xlabel('时间', fontdict=font_props['label'])
                self.ax.set_ylabel('数值', fontdict=font_props['label'])

                if self.legend_var.get():
                    self.ax.legend(prop=font_props['legend'])

                self.ax.grid(True, alpha=0.3)

            except Exception as plot_error:
                print(f"绘制数据概览图时出错: {plot_error}")
                self.ax.text(0.5, 0.5, f'绘制失败\n{str(plot_error)}',
                            ha='center', va='center', transform=self.ax.transAxes,
                            fontsize=14, alpha=0.7, fontfamily=font_props['title']['family'])
                self.ax.set_title('数据概览', fontdict=font_props['title'])

        except Exception as e:
            print(f"数据概览图生成失败: {e}")
            # 获取字体属性（如果之前失败了）
            try:
                font_props = self._get_font_props()
            except:
                font_props = {'title': {'family': 'SimHei', 'size': 14}}

            self.ax.text(0.5, 0.5, f'图表生成失败\n{str(e)}',
                        ha='center', va='center', transform=self.ax.transAxes,
                        fontsize=14, alpha=0.7, fontfamily=font_props['title']['family'])
            self.ax.set_title('数据概览', fontdict=font_props['title'])
            
    def plot_time_series(self):
        """绘制时间序列图"""
        # 获取字体属性
        font_props = self._get_font_props()

        if not self.main_app.data_manager.has_data():
            self.ax.text(0.5, 0.5, '暂无数据', ha='center', va='center',
                        transform=self.ax.transAxes, fontsize=16, alpha=0.5,
                        fontfamily=font_props['title']['family'])
            return

        # 模拟时间序列数据
        dates = pd.date_range('2024-01-01', periods=100, freq='H')
        values = 50 + 20 * np.sin(np.arange(100) * 2 * np.pi / 24) + np.random.normal(0, 5, 100)

        self.ax.plot(dates, values, linewidth=2, color='blue', alpha=0.8)
        self.ax.set_title('充电负荷时间序列', fontdict=font_props['title'])
        self.ax.set_xlabel('时间', fontdict=font_props['label'])
        self.ax.set_ylabel('充电负荷 (kW)', fontdict=font_props['label'])
        
    def plot_correlation(self):
        """绘制相关性分析图"""
        try:
            # 获取字体属性
            font_props = self._get_font_props()

            # 检查数据是否可用
            if not hasattr(self.main_app, 'data_manager') or not self.main_app.data_manager.has_data():
                self.ax.text(0.5, 0.5, '暂无数据', ha='center', va='center',
                            transform=self.ax.transAxes, fontsize=16, alpha=0.5,
                            fontfamily=font_props['title']['family'])
                self.ax.set_title('相关性分析', fontdict=font_props['title'])
                return

            # 尝试获取真实数据进行相关性分析
            data = self.main_app.data_manager.get_data()
            if data is not None and not data.empty:
                numeric_cols = data.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) >= 2:
                    # 使用真实数据计算相关性
                    corr_data = data[numeric_cols].corr()
                    features = list(numeric_cols[:7])  # 最多显示7个特征
                    corr_matrix = corr_data.loc[features, features].values
                else:
                    # 如果数值列不足，使用模拟数据
                    features = ['A相电压', 'A相电流', '降水量', '平均气温', '最低气温', '最高气温', '总有功功率']
                    corr_matrix = np.random.rand(len(features), len(features))
                    corr_matrix = (corr_matrix + corr_matrix.T) / 2  # 使矩阵对称
                    np.fill_diagonal(corr_matrix, 1)  # 对角线为1
            else:
                # 使用模拟数据
                features = ['A相电压', 'A相电流', '降水量', '平均气温', '最低气温', '最高气温', '总有功功率']
                corr_matrix = np.random.rand(len(features), len(features))
                corr_matrix = (corr_matrix + corr_matrix.T) / 2  # 使矩阵对称
                np.fill_diagonal(corr_matrix, 1)  # 对角线为1

            # 绘制热力图
            im = self.ax.imshow(corr_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)

            # 设置标签
            self.ax.set_xticks(range(len(features)))
            self.ax.set_yticks(range(len(features)))
            self.ax.set_xticklabels(features, rotation=45, ha='right', fontdict=font_props['label'])
            self.ax.set_yticklabels(features, fontdict=font_props['label'])

            # 添加数值标注
            for i in range(len(features)):
                for j in range(len(features)):
                    self.ax.text(j, i, f'{corr_matrix[i, j]:.2f}',
                               ha='center', va='center', color='white' if abs(corr_matrix[i, j]) > 0.5 else 'black',
                               fontfamily=font_props['label']['family'])

            self.ax.set_title('特征相关性分析', fontdict=font_props['title'])

            # 添加颜色条
            cbar = self.fig.colorbar(im, ax=self.ax)
            cbar.set_label('相关系数', fontdict=font_props['label'])

        except Exception as e:
            print(f"相关性分析图生成失败: {e}")
            try:
                font_props = self._get_font_props()
            except:
                font_props = {'title': {'family': 'SimHei', 'size': 14}}

            self.ax.text(0.5, 0.5, f'相关性分析失败\n{str(e)}',
                        ha='center', va='center', transform=self.ax.transAxes,
                        fontsize=14, alpha=0.7, fontfamily=font_props['title']['family'])
            self.ax.set_title('相关性分析', fontdict=font_props['title'])
        
    def plot_distribution(self):
        """绘制分布分析图"""
        # 获取字体属性
        font_props = self._get_font_props()

        # 生成模拟数据
        data = np.random.normal(50, 15, 1000)

        # 绘制直方图和密度曲线
        self.ax.hist(data, bins=30, density=True, alpha=0.7, color='skyblue', edgecolor='black')

        # 添加密度曲线
        x = np.linspace(data.min(), data.max(), 100)
        y = ((1/np.sqrt(2*np.pi*15**2)) * np.exp(-0.5*((x-50)/15)**2))
        self.ax.plot(x, y, 'r-', linewidth=2, label='理论分布')

        self.ax.set_title('充电负荷分布分析', fontdict=font_props['title'])
        self.ax.set_xlabel('充电负荷 (kW)', fontdict=font_props['label'])
        self.ax.set_ylabel('密度', fontdict=font_props['label'])

        if self.legend_var.get():
            self.ax.legend(prop=font_props['legend'])
            
    def plot_prediction_comparison(self):
        """绘制预测对比图"""
        # 获取字体属性
        font_props = self._get_font_props()

        # 生成模拟预测数据
        x = np.arange(50)
        true_values = 50 + 20 * np.sin(x * 2 * np.pi / 24) + np.random.normal(0, 3, 50)
        pred_values = true_values + np.random.normal(0, 5, 50)

        self.ax.plot(x, true_values, 'b-', label='真实值', linewidth=2, marker='o', markersize=4)
        self.ax.plot(x, pred_values, 'r--', label='预测值', linewidth=2, marker='s', markersize=4)

        self.ax.set_title('预测结果对比', fontdict=font_props['title'])
        self.ax.set_xlabel('时间点', fontdict=font_props['label'])
        self.ax.set_ylabel('充电负荷 (kW)', fontdict=font_props['label'])

        if self.legend_var.get():
            self.ax.legend(prop=font_props['legend'])
            
    def plot_feature_importance(self):
        """绘制特征重要性图"""
        # 获取字体属性
        font_props = self._get_font_props()

        features = ['A相电压', 'A相电流', '降水量', '平均气温', '最低气温', '最高气温', '总有功功率']
        importance = np.random.rand(len(features))
        importance = importance / importance.sum()  # 归一化

        # 按重要性排序
        sorted_idx = np.argsort(importance)
        features_sorted = [features[i] for i in sorted_idx]
        importance_sorted = importance[sorted_idx]

        bars = self.ax.barh(range(len(features_sorted)), importance_sorted,
                           color='lightgreen', alpha=0.8)

        self.ax.set_yticks(range(len(features_sorted)))
        self.ax.set_yticklabels(features_sorted, fontdict=font_props['label'])
        self.ax.set_xlabel('重要性', fontdict=font_props['label'])
        self.ax.set_title('特征重要性分析', fontdict=font_props['title'])

        # 添加数值标签
        for i, (bar, imp) in enumerate(zip(bars, importance_sorted)):
            self.ax.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2,
                        f'{imp:.3f}', va='center', fontfamily=font_props['label']['family'])
                        
    def plot_training_history(self):
        """绘制训练历史图"""
        # 确保字体配置
        font_manager = get_font_manager()

        # 设置中文字体属性
        font_prop = {'family': font_manager.matplotlib_font, 'size': 12}
        label_font_prop = {'family': font_manager.matplotlib_font, 'size': 10}
        legend_font_prop = {'family': font_manager.matplotlib_font, 'size': 9}

        # 生成模拟训练历史
        epochs = np.arange(1, 51)
        train_loss = 1.0 * np.exp(-epochs/20) + 0.1 + np.random.normal(0, 0.05, 50)
        val_loss = 1.2 * np.exp(-epochs/25) + 0.15 + np.random.normal(0, 0.08, 50)

        self.ax.plot(epochs, train_loss, 'b-', label='训练损失', linewidth=2)
        self.ax.plot(epochs, val_loss, 'r-', label='验证损失', linewidth=2)

        # 设置标题和坐标轴标签，明确指定字体
        self.ax.set_title('训练历史', fontdict=font_prop)
        self.ax.set_xlabel('训练轮次', fontdict=label_font_prop)
        self.ax.set_ylabel('损失值', fontdict=label_font_prop)

        if self.legend_var.get():
            self.ax.legend(prop=legend_font_prop)
            
    def plot_performance_metrics(self):
        """绘制性能指标图"""
        # 获取字体属性
        font_props = self._get_font_props()

        metrics = ['MSE', 'RMSE', 'MAE', 'R²', 'MAPE']
        values = [0.025, 0.158, 0.123, 0.892, 8.5]
        colors = ['red', 'orange', 'yellow', 'green', 'blue']

        bars = self.ax.bar(metrics, values, color=colors, alpha=0.7)

        self.ax.set_title('模型性能指标', fontdict=font_props['title'])
        self.ax.set_ylabel('指标值', fontdict=font_props['label'])

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            self.ax.text(bar.get_x() + bar.get_width()/2., height,
                        f'{value:.3f}', ha='center', va='bottom',
                        fontfamily=font_props['label']['family'])
                        
    def apply_chart_style(self):
        """应用图表样式"""
        try:
            # 设置样式
            style_name = self.style_var.get()

            # 检查样式是否可用
            if style_name in plt.style.available:
                plt.style.use(style_name)
            else:
                print(f"样式 '{style_name}' 不可用，使用默认样式")
                plt.style.use('default')

            # 设置网格
            if self.grid_var.get():
                self.ax.grid(True, alpha=0.3)

            # 调整布局
            self.fig.tight_layout()

        except Exception as e:
            print(f"应用图表样式失败: {e}")
            # 使用默认样式
            try:
                plt.style.use('default')
                if self.grid_var.get():
                    self.ax.grid(True, alpha=0.3)
                self.fig.tight_layout()
            except Exception as fallback_error:
                print(f"应用默认样式也失败: {fallback_error}")
        
    def refresh_chart(self):
        """刷新图表"""
        self.update_chart()
        
    def save_chart(self):
        """保存图表"""
        try:
            # 使用结果管理器自动保存到当前会话目录
            results_manager = get_results_manager()
            chart_type = self.chart_type_var.get()
            plot_path = results_manager.save_figure(self.fig, f"visualization_{chart_type}")
            messagebox.showinfo("成功", f"可视化图表已保存到: {plot_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

    def save_chart_manual(self):
        """手动选择路径保存图表"""
        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[
                ("PNG文件", "*.png"),
                ("PDF文件", "*.pdf"),
                ("SVG文件", "*.svg"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                self.fig.savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
                
    def copy_data(self):
        """复制数据到剪贴板"""
        messagebox.showinfo("提示", "数据复制功能正在开发中...")
        
    def export_data(self):
        """导出数据"""
        messagebox.showinfo("提示", "数据导出功能正在开发中...")
        
    def zoom_in(self):
        """放大图表"""
        xlim = self.ax.get_xlim()
        ylim = self.ax.get_ylim()
        
        x_center = (xlim[0] + xlim[1]) / 2
        y_center = (ylim[0] + ylim[1]) / 2
        
        x_range = (xlim[1] - xlim[0]) * 0.8
        y_range = (ylim[1] - ylim[0]) * 0.8
        
        self.ax.set_xlim(x_center - x_range/2, x_center + x_range/2)
        self.ax.set_ylim(y_center - y_range/2, y_center + y_range/2)
        
        self.canvas.draw()
        
    def zoom_out(self):
        """缩小图表"""
        xlim = self.ax.get_xlim()
        ylim = self.ax.get_ylim()
        
        x_center = (xlim[0] + xlim[1]) / 2
        y_center = (ylim[0] + ylim[1]) / 2
        
        x_range = (xlim[1] - xlim[0]) * 1.25
        y_range = (ylim[1] - ylim[0]) * 1.25
        
        self.ax.set_xlim(x_center - x_range/2, x_center + x_range/2)
        self.ax.set_ylim(y_center - y_range/2, y_center + y_range/2)
        
        self.canvas.draw()
        
    def zoom_home(self):
        """重置缩放"""
        self.ax.autoscale()
        self.canvas.draw()
    
    def get_frame(self):
        """获取面板框架"""
        return self.frame
