"""
指标面板 - 提供模型性能指标的展示和分析
Metrics Panel - Provides model performance metrics display and analysis
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np


class MetricsPanel:
    """指标面板类"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)

        # 获取字体管理器
        try:
            from gui.utils.gui_utils import get_font_manager
            self.font_manager = get_font_manager()
        except:
            self.font_manager = None

        self.create_widgets()

    def _get_font_props(self):
        """获取字体属性"""
        if self.font_manager:
            return {
                'title': {'family': self.font_manager.matplotlib_font, 'size': 12},
                'label': {'family': self.font_manager.matplotlib_font, 'size': 10},
                'legend': {'family': self.font_manager.matplotlib_font, 'size': 9}
            }
        else:
            return {
                'title': {'family': 'Microsoft YaHei', 'size': 12},
                'label': {'family': 'Microsoft YaHei', 'size': 10},
                'legend': {'family': 'Microsoft YaHei', 'size': 9}
            }
        
    def create_widgets(self):
        """创建界面组件"""
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部：指标概览
        self.create_metrics_overview(main_container)
        
        # 底部：指标可视化
        self.create_metrics_visualization(main_container)
        
    def create_metrics_overview(self, parent):
        """创建指标概览区域"""
        overview_frame = ttk.LabelFrame(parent, text="📊 性能指标概览", padding=15)
        overview_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建指标网格
        metrics_grid = ttk.Frame(overview_frame)
        metrics_grid.pack(fill=tk.X)
        
        # 第一行指标
        row1_frame = ttk.Frame(metrics_grid)
        row1_frame.pack(fill=tk.X, pady=(0, 10))
        
        # MSE
        mse_frame = ttk.Frame(row1_frame)
        mse_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Label(mse_frame, text="均方误差 (MSE)", font=("Arial", 10, "bold")).pack()
        self.mse_var = tk.StringVar(value="N/A")
        ttk.Label(mse_frame, textvariable=self.mse_var, font=("Arial", 14, "bold"), 
                 foreground="blue").pack()
        
        # RMSE
        rmse_frame = ttk.Frame(row1_frame)
        rmse_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Label(rmse_frame, text="均方根误差 (RMSE)", font=("Arial", 10, "bold")).pack()
        self.rmse_var = tk.StringVar(value="N/A")
        ttk.Label(rmse_frame, textvariable=self.rmse_var, font=("Arial", 14, "bold"), 
                 foreground="green").pack()
        
        # MAE
        mae_frame = ttk.Frame(row1_frame)
        mae_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Label(mae_frame, text="平均绝对误差 (MAE)", font=("Arial", 10, "bold")).pack()
        self.mae_var = tk.StringVar(value="N/A")
        ttk.Label(mae_frame, textvariable=self.mae_var, font=("Arial", 14, "bold"), 
                 foreground="orange").pack()
        
        # R²
        r2_frame = ttk.Frame(row1_frame)
        r2_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(r2_frame, text="决定系数 (R²)", font=("Arial", 10, "bold")).pack()
        self.r2_var = tk.StringVar(value="N/A")
        ttk.Label(r2_frame, textvariable=self.r2_var, font=("Arial", 14, "bold"), 
                 foreground="red").pack()
        
        # 第二行指标
        row2_frame = ttk.Frame(metrics_grid)
        row2_frame.pack(fill=tk.X)
        
        # MAPE
        mape_frame = ttk.Frame(row2_frame)
        mape_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Label(mape_frame, text="平均绝对百分比误差 (MAPE)", font=("Arial", 10, "bold")).pack()
        self.mape_var = tk.StringVar(value="N/A")
        ttk.Label(mape_frame, textvariable=self.mape_var, font=("Arial", 14, "bold"), 
                 foreground="purple").pack()
        
        # 预测准确率
        accuracy_frame = ttk.Frame(row2_frame)
        accuracy_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Label(accuracy_frame, text="预测准确率", font=("Arial", 10, "bold")).pack()
        self.accuracy_var = tk.StringVar(value="N/A")
        ttk.Label(accuracy_frame, textvariable=self.accuracy_var, font=("Arial", 14, "bold"), 
                 foreground="darkgreen").pack()
        
        # 训练时间
        time_frame = ttk.Frame(row2_frame)
        time_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Label(time_frame, text="训练时间", font=("Arial", 10, "bold")).pack()
        self.training_time_var = tk.StringVar(value="N/A")
        ttk.Label(time_frame, textvariable=self.training_time_var, font=("Arial", 14, "bold"), 
                 foreground="brown").pack()
        
        # 模型大小
        size_frame = ttk.Frame(row2_frame)
        size_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(size_frame, text="模型大小", font=("Arial", 10, "bold")).pack()
        self.model_size_var = tk.StringVar(value="N/A")
        ttk.Label(size_frame, textvariable=self.model_size_var, font=("Arial", 14, "bold"), 
                 foreground="navy").pack()
        
    def create_metrics_visualization(self, parent):
        """创建指标可视化区域"""
        viz_frame = ttk.LabelFrame(parent, text="📈 指标可视化", padding=10)
        viz_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建笔记本控件
        self.viz_notebook = ttk.Notebook(viz_frame)
        self.viz_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 指标对比图
        self.create_metrics_comparison_tab()
        
        # 指标趋势图
        self.create_metrics_trend_tab()
        
        # 指标雷达图
        self.create_metrics_radar_tab()
        
    def create_metrics_comparison_tab(self):
        """创建指标对比图标签页"""
        comparison_tab = ttk.Frame(self.viz_notebook)
        self.viz_notebook.add(comparison_tab, text="📊 指标对比")

        # 创建matplotlib图表
        self.comp_fig, self.comp_ax = plt.subplots(figsize=(8, 6))

        # 获取字体属性并设置初始标题
        font_props = self._get_font_props()
        self.comp_ax.set_title('模型性能指标对比', fontdict=font_props['title'])
        self.comp_ax.set_ylabel('指标值', fontdict=font_props['label'])
        
        # 嵌入到tkinter
        self.comp_canvas = FigureCanvasTkAgg(self.comp_fig, comparison_tab)
        self.comp_canvas.draw()
        self.comp_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 控制按钮
        comp_controls = ttk.Frame(comparison_tab)
        comp_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(comp_controls, text="🔄 刷新", 
                  command=self.refresh_comparison_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(comp_controls, text="💾 保存", 
                  command=self.save_comparison_plot).pack(side=tk.LEFT)
        
    def create_metrics_trend_tab(self):
        """创建指标趋势图标签页"""
        trend_tab = ttk.Frame(self.viz_notebook)
        self.viz_notebook.add(trend_tab, text="📈 指标趋势")

        # 创建matplotlib图表
        self.trend_fig, self.trend_ax = plt.subplots(figsize=(8, 6))

        # 获取字体属性并设置初始标题和标签
        font_props = self._get_font_props()
        self.trend_ax.set_title('训练过程指标变化趋势', fontdict=font_props['title'])
        self.trend_ax.set_xlabel('训练轮次', fontdict=font_props['label'])
        self.trend_ax.set_ylabel('损失值', fontdict=font_props['label'])
        self.trend_ax.grid(True, alpha=0.3)
        
        # 嵌入到tkinter
        self.trend_canvas = FigureCanvasTkAgg(self.trend_fig, trend_tab)
        self.trend_canvas.draw()
        self.trend_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 控制按钮
        trend_controls = ttk.Frame(trend_tab)
        trend_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(trend_controls, text="🔄 刷新", 
                  command=self.refresh_trend_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(trend_controls, text="💾 保存", 
                  command=self.save_trend_plot).pack(side=tk.LEFT)
        
    def create_metrics_radar_tab(self):
        """创建指标雷达图标签页"""
        radar_tab = ttk.Frame(self.viz_notebook)
        self.viz_notebook.add(radar_tab, text="🎯 雷达图")

        # 创建matplotlib图表（极坐标）
        self.radar_fig = plt.figure(figsize=(8, 6))
        self.radar_ax = self.radar_fig.add_subplot(111, projection='polar')

        # 获取字体属性并设置初始标题
        font_props = self._get_font_props()
        self.radar_ax.set_title('模型性能雷达图', pad=20, fontdict=font_props['title'])
        
        # 嵌入到tkinter
        self.radar_canvas = FigureCanvasTkAgg(self.radar_fig, radar_tab)
        self.radar_canvas.draw()
        self.radar_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 控制按钮
        radar_controls = ttk.Frame(radar_tab)
        radar_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(radar_controls, text="🔄 刷新", 
                  command=self.refresh_radar_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(radar_controls, text="💾 保存", 
                  command=self.save_radar_plot).pack(side=tk.LEFT)
        
    def update_metrics(self, metrics_data):
        """更新指标数据"""
        if not metrics_data:
            return
        
        # 更新指标显示
        self.mse_var.set(f"{metrics_data.get('mse', 0):.6f}")
        self.rmse_var.set(f"{metrics_data.get('rmse', 0):.6f}")
        self.mae_var.set(f"{metrics_data.get('mae', 0):.6f}")
        self.r2_var.set(f"{metrics_data.get('r2', 0):.6f}")
        
        # 计算MAPE和准确率（如果有原始数据）
        mape = metrics_data.get('mape', 0)
        accuracy = metrics_data.get('accuracy', 0)
        
        self.mape_var.set(f"{mape:.2f}%")
        self.accuracy_var.set(f"{accuracy:.2f}%")
        
        # 更新其他信息
        training_time = metrics_data.get('training_time', 'N/A')
        model_size = metrics_data.get('model_size', 'N/A')
        
        self.training_time_var.set(str(training_time))
        self.model_size_var.set(str(model_size))
        
        # 更新可视化
        self.update_all_plots(metrics_data)
        
    def update_all_plots(self, metrics_data):
        """更新所有图表"""
        self.update_comparison_plot(metrics_data)
        self.update_trend_plot(metrics_data)
        self.update_radar_plot(metrics_data)
        
    def update_comparison_plot(self, metrics_data):
        """更新指标对比图"""
        self.comp_ax.clear()

        # 获取字体属性
        font_props = self._get_font_props()

        # 准备数据
        metrics_names = ['MSE', 'RMSE', 'MAE', 'R²', 'MAPE']
        metrics_values = [
            metrics_data.get('mse', 0),
            metrics_data.get('rmse', 0),
            metrics_data.get('mae', 0),
            metrics_data.get('r2', 0),
            metrics_data.get('mape', 0) / 100  # 转换为小数
        ]

        # 创建条形图
        colors = ['blue', 'green', 'orange', 'red', 'purple']
        bars = self.comp_ax.bar(metrics_names, metrics_values, color=colors, alpha=0.7)

        # 添加数值标签
        for bar, value in zip(bars, metrics_values):
            height = bar.get_height()
            self.comp_ax.text(bar.get_x() + bar.get_width()/2., height,
                             f'{value:.4f}', ha='center', va='bottom',
                             fontfamily=font_props['label']['family'])

        self.comp_ax.set_title('模型性能指标对比', fontdict=font_props['title'])
        self.comp_ax.set_ylabel('指标值', fontdict=font_props['label'])
        self.comp_ax.grid(True, alpha=0.3)

        self.comp_canvas.draw()
        
    def update_trend_plot(self, metrics_data):
        """更新指标趋势图"""
        self.trend_ax.clear()

        # 获取字体属性
        font_props = self._get_font_props()

        # 获取训练历史数据
        training_history = self.main_app.model_manager.get_training_history()

        if training_history:
            # 提取最近一次训练的损失数据
            latest_training = training_history[-1]
            epoch_losses = latest_training.get('training_result', {}).get('epoch_losses', [])

            if epoch_losses:
                epochs = [item['epoch'] for item in epoch_losses]
                train_losses = [item['train_loss'] for item in epoch_losses]
                val_losses = [item['val_loss'] for item in epoch_losses]

                self.trend_ax.plot(epochs, train_losses, label='训练损失', marker='o', markersize=4)
                self.trend_ax.plot(epochs, val_losses, label='验证损失', marker='s', markersize=4)

                self.trend_ax.set_xlabel('训练轮次', fontdict=font_props['label'])
                self.trend_ax.set_ylabel('损失值', fontdict=font_props['label'])
                self.trend_ax.legend(prop=font_props['legend'])

        self.trend_ax.set_title('训练过程指标变化趋势', fontdict=font_props['title'])
        self.trend_ax.grid(True, alpha=0.3)

        self.trend_canvas.draw()
        
    def update_radar_plot(self, metrics_data):
        """更新雷达图"""
        self.radar_ax.clear()

        # 获取字体属性
        font_props = self._get_font_props()

        # 准备雷达图数据
        categories = ['准确性', '稳定性', '效率', '泛化能力', '鲁棒性']

        # 根据指标计算各维度得分（0-1之间）
        r2_score = max(0, metrics_data.get('r2', 0))  # R²可能为负
        mse_score = 1 / (1 + metrics_data.get('mse', 1))  # MSE越小越好
        mae_score = 1 / (1 + metrics_data.get('mae', 1))  # MAE越小越好
        rmse_score = 1 / (1 + metrics_data.get('rmse', 1))  # RMSE越小越好

        # 模拟其他维度得分
        efficiency_score = 0.8  # 效率得分

        values = [r2_score, rmse_score, efficiency_score, mae_score, mse_score]

        # 计算角度
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]  # 闭合图形
        angles += angles[:1]

        # 绘制雷达图
        self.radar_ax.plot(angles, values, 'o-', linewidth=2, label='当前模型')
        self.radar_ax.fill(angles, values, alpha=0.25)

        # 设置标签
        self.radar_ax.set_xticks(angles[:-1])
        self.radar_ax.set_xticklabels(categories, fontdict=font_props['label'])
        self.radar_ax.set_ylim(0, 1)

        # 添加网格线
        self.radar_ax.grid(True)

        self.radar_ax.set_title('模型性能雷达图', pad=20, fontdict=font_props['title'])

        self.radar_canvas.draw()
        
    def refresh_comparison_plot(self):
        """刷新对比图"""
        # 获取最新的指标数据
        metrics_data = self.get_current_metrics()
        if metrics_data:
            self.update_comparison_plot(metrics_data)
            
    def refresh_trend_plot(self):
        """刷新趋势图"""
        metrics_data = self.get_current_metrics()
        if metrics_data:
            self.update_trend_plot(metrics_data)
            
    def refresh_radar_plot(self):
        """刷新雷达图"""
        metrics_data = self.get_current_metrics()
        if metrics_data:
            self.update_radar_plot(metrics_data)
            
    def get_current_metrics(self):
        """获取当前指标数据"""
        # 从训练历史中获取最新的指标数据
        training_history = self.main_app.model_manager.get_training_history()
        
        if training_history:
            latest_training = training_history[-1]
            return latest_training.get('test_metrics', {})
        
        return {}
        
    def save_comparison_plot(self):
        """保存对比图"""
        from tkinter import filedialog
        
        file_path = filedialog.asksaveasfilename(
            title="保存指标对比图",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf")]
        )
        
        if file_path:
            self.comp_fig.savefig(file_path, dpi=300, bbox_inches='tight')
            
    def save_trend_plot(self):
        """保存趋势图"""
        from tkinter import filedialog
        
        file_path = filedialog.asksaveasfilename(
            title="保存趋势图",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf")]
        )
        
        if file_path:
            self.trend_fig.savefig(file_path, dpi=300, bbox_inches='tight')
            
    def save_radar_plot(self):
        """保存雷达图"""
        from tkinter import filedialog
        
        file_path = filedialog.asksaveasfilename(
            title="保存雷达图",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf")]
        )
        
        if file_path:
            self.radar_fig.savefig(file_path, dpi=300, bbox_inches='tight')
    
    def get_frame(self):
        """获取面板框架"""
        return self.frame
