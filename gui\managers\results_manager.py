#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一结果管理器
Unified Results Manager for Training Session Management
"""

import os
import time
import json
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any
import matplotlib.pyplot as plt
import pandas as pd


class ResultsManager:
    """统一结果管理器 - 管理每次训练会话的所有结果文件"""
    
    def __init__(self, base_results_dir: str = "results"):
        """
        初始化结果管理器
        
        Args:
            base_results_dir: 基础结果目录
        """
        self.base_results_dir = base_results_dir
        self.session_timestamp = None
        self.session_dir = None
        self.session_info = {}
        
        # 确保基础结果目录存在
        os.makedirs(self.base_results_dir, exist_ok=True)
    
    def start_new_session(self, session_name: str = None) -> str:
        """
        开始新的训练会话
        
        Args:
            session_name: 可选的会话名称
            
        Returns:
            str: 会话目录路径
        """
        # 创建时间戳
        self.session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建会话目录名
        if session_name:
            session_dir_name = f"{self.session_timestamp}_{session_name}"
        else:
            session_dir_name = self.session_timestamp
        
        # 创建会话目录
        self.session_dir = os.path.join(self.base_results_dir, session_dir_name)
        os.makedirs(self.session_dir, exist_ok=True)
        
        # 创建子目录
        self._create_session_subdirs()
        
        # 初始化会话信息
        self.session_info = {
            "session_id": self.session_timestamp,
            "session_name": session_name or "default",
            "start_time": datetime.now().isoformat(),
            "session_dir": self.session_dir,
            "files": {
                "models": [],
                "plots": [],
                "data": [],
                "logs": [],
                "configs": []
            }
        }
        
        # 保存会话信息
        self._save_session_info()
        
        print(f"🎯 新训练会话已开始: {session_dir_name}")
        print(f"📁 结果保存目录: {self.session_dir}")
        
        return self.session_dir
    
    def _create_session_subdirs(self):
        """创建会话子目录"""
        subdirs = [
            "models",      # 模型文件
            "plots",       # 图表文件
            "data",        # 数据文件
            "logs",        # 日志文件
            "configs"      # 配置文件
        ]
        
        for subdir in subdirs:
            os.makedirs(os.path.join(self.session_dir, subdir), exist_ok=True)
    
    def get_model_save_path(self, model_name: str = "best_model.pth") -> str:
        """
        获取模型保存路径
        
        Args:
            model_name: 模型文件名
            
        Returns:
            str: 完整的模型保存路径
        """
        if not self.session_dir:
            raise ValueError("请先调用 start_new_session() 开始新会话")
        
        # 添加时间戳到模型名
        name, ext = os.path.splitext(model_name)
        timestamped_name = f"{name}_{self.session_timestamp}{ext}"
        
        model_path = os.path.join(self.session_dir, "models", timestamped_name)
        
        # 记录模型文件
        self.session_info["files"]["models"].append({
            "filename": timestamped_name,
            "path": model_path,
            "created_at": datetime.now().isoformat()
        })
        
        return model_path
    
    def get_plot_save_path(self, plot_name: str) -> str:
        """
        获取图表保存路径
        
        Args:
            plot_name: 图表文件名（不含扩展名）
            
        Returns:
            str: 完整的图表保存路径
        """
        if not self.session_dir:
            raise ValueError("请先调用 start_new_session() 开始新会话")
        
        # 添加时间戳到图表名
        timestamped_name = f"{plot_name}_{self.session_timestamp}.png"
        plot_path = os.path.join(self.session_dir, "plots", timestamped_name)
        
        # 记录图表文件
        self.session_info["files"]["plots"].append({
            "filename": timestamped_name,
            "path": plot_path,
            "created_at": datetime.now().isoformat()
        })
        
        return plot_path
    
    def get_data_save_path(self, data_name: str, extension: str = ".csv") -> str:
        """
        获取数据保存路径
        
        Args:
            data_name: 数据文件名（不含扩展名）
            extension: 文件扩展名
            
        Returns:
            str: 完整的数据保存路径
        """
        if not self.session_dir:
            raise ValueError("请先调用 start_new_session() 开始新会话")
        
        # 添加时间戳到数据文件名
        timestamped_name = f"{data_name}_{self.session_timestamp}{extension}"
        data_path = os.path.join(self.session_dir, "data", timestamped_name)
        
        # 记录数据文件
        self.session_info["files"]["data"].append({
            "filename": timestamped_name,
            "path": data_path,
            "created_at": datetime.now().isoformat()
        })
        
        return data_path
    
    def get_config_save_path(self, config_name: str = "training_config.json") -> str:
        """
        获取配置保存路径
        
        Args:
            config_name: 配置文件名
            
        Returns:
            str: 完整的配置保存路径
        """
        if not self.session_dir:
            raise ValueError("请先调用 start_new_session() 开始新会话")
        
        # 添加时间戳到配置文件名
        name, ext = os.path.splitext(config_name)
        timestamped_name = f"{name}_{self.session_timestamp}{ext}"
        config_path = os.path.join(self.session_dir, "configs", timestamped_name)
        
        # 记录配置文件
        self.session_info["files"]["configs"].append({
            "filename": timestamped_name,
            "path": config_path,
            "created_at": datetime.now().isoformat()
        })
        
        return config_path
    
    def save_training_config(self, config: Dict[str, Any]):
        """
        保存训练配置
        
        Args:
            config: 训练配置字典
        """
        config_path = self.get_config_save_path("training_config.json")
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 训练配置已保存: {config_path}")
    
    def save_figure(self, fig: plt.Figure, plot_name: str, **kwargs):
        """
        保存matplotlib图表
        
        Args:
            fig: matplotlib图表对象
            plot_name: 图表名称
            **kwargs: savefig的额外参数
        """
        plot_path = self.get_plot_save_path(plot_name)
        
        # 默认参数
        save_kwargs = {
            'dpi': 300,
            'bbox_inches': 'tight',
            'facecolor': 'white',
            'edgecolor': 'none'
        }
        save_kwargs.update(kwargs)
        
        fig.savefig(plot_path, **save_kwargs)
        print(f"✅ 图表已保存: {plot_path}")
        
        return plot_path
    
    def save_dataframe(self, df: pd.DataFrame, data_name: str, **kwargs):
        """
        保存pandas DataFrame
        
        Args:
            df: pandas DataFrame
            data_name: 数据名称
            **kwargs: to_csv的额外参数
        """
        data_path = self.get_data_save_path(data_name, ".csv")
        
        # 默认参数
        save_kwargs = {
            'index': False,
            'encoding': 'utf-8'
        }
        save_kwargs.update(kwargs)
        
        df.to_csv(data_path, **save_kwargs)
        print(f"✅ 数据已保存: {data_path}")
        
        return data_path
    
    def _save_session_info(self):
        """保存会话信息"""
        if self.session_dir:
            info_path = os.path.join(self.session_dir, "session_info.json")
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(self.session_info, f, indent=2, ensure_ascii=False)
    
    def end_session(self, training_metrics: Dict[str, Any] = None):
        """
        结束训练会话
        
        Args:
            training_metrics: 训练指标
        """
        if not self.session_dir:
            return
        
        # 更新会话信息
        self.session_info["end_time"] = datetime.now().isoformat()
        if training_metrics:
            self.session_info["training_metrics"] = training_metrics
        
        # 保存最终会话信息
        self._save_session_info()
        
        # 创建会话总结
        self._create_session_summary()
        
        print(f"🎉 训练会话已结束: {self.session_dir}")
        print(f"📊 共保存 {len(self.session_info['files']['models'])} 个模型文件")
        print(f"📈 共保存 {len(self.session_info['files']['plots'])} 个图表文件")
    
    def _create_session_summary(self):
        """创建会话总结文件"""
        summary_path = os.path.join(self.session_dir, "session_summary.md")
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(f"# 训练会话总结\n\n")
            f.write(f"**会话ID**: {self.session_info['session_id']}\n")
            f.write(f"**会话名称**: {self.session_info['session_name']}\n")
            f.write(f"**开始时间**: {self.session_info['start_time']}\n")
            f.write(f"**结束时间**: {self.session_info.get('end_time', '未结束')}\n\n")
            
            # 文件统计
            f.write("## 文件统计\n\n")
            for file_type, files in self.session_info['files'].items():
                f.write(f"- **{file_type}**: {len(files)} 个文件\n")
            
            f.write("\n## 文件列表\n\n")
            for file_type, files in self.session_info['files'].items():
                if files:
                    f.write(f"### {file_type.title()}\n\n")
                    for file_info in files:
                        f.write(f"- `{file_info['filename']}` ({file_info['created_at']})\n")
                    f.write("\n")
            
            # 训练指标
            if 'training_metrics' in self.session_info:
                f.write("## 训练指标\n\n")
                for metric, value in self.session_info['training_metrics'].items():
                    f.write(f"- **{metric}**: {value}\n")
        
        print(f"📝 会话总结已保存: {summary_path}")
    
    def get_current_session_dir(self) -> Optional[str]:
        """获取当前会话目录"""
        return self.session_dir
    
    def list_sessions(self) -> List[str]:
        """列出所有历史会话"""
        if not os.path.exists(self.base_results_dir):
            return []
        
        sessions = []
        for item in os.listdir(self.base_results_dir):
            item_path = os.path.join(self.base_results_dir, item)
            if os.path.isdir(item_path) and item.replace('_', '').replace('-', '').isdigit():
                sessions.append(item)
        
        return sorted(sessions, reverse=True)  # 最新的在前


# 全局结果管理器实例
_results_manager = None


def get_results_manager() -> ResultsManager:
    """获取全局结果管理器实例"""
    global _results_manager
    if _results_manager is None:
        _results_manager = ResultsManager()
    return _results_manager


def start_training_session(session_name: str = None) -> str:
    """开始新的训练会话"""
    manager = get_results_manager()
    return manager.start_new_session(session_name)


def end_training_session(training_metrics: Dict[str, Any] = None):
    """结束当前训练会话"""
    manager = get_results_manager()
    manager.end_session(training_metrics)
