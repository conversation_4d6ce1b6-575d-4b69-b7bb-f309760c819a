import sys
import os
import time
import tkinter as tk
from tkinter import messagebox
import traceback
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
def setup_logging():
    """设置日志配置"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "gui.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'tkinter',
        'numpy',
        'pandas',
        'matplotlib',
        'seaborn',
        'scikit-learn',
        'torch'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'numpy':
                import numpy
            elif package == 'pandas':
                import pandas
            elif package == 'matplotlib':
                import matplotlib
            elif package == 'seaborn':
                import seaborn
            elif package == 'scikit-learn':
                import sklearn
            elif package == 'torch':
                import torch
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        error_msg = f"缺少以下依赖包: {', '.join(missing_packages)}\n\n"
        error_msg += "请使用以下命令安装:\n"
        error_msg += f"pip install {' '.join(missing_packages)}"
        
        # 创建简单的错误对话框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("依赖包缺失", error_msg)
        root.destroy()
        return False
    
    return True

def check_system_requirements():
    """检查系统要求"""
    # 检查Python版本
    if sys.version_info < (3, 7):
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Python版本错误", 
                           f"需要Python 3.7或更高版本，当前版本: {sys.version}")
        root.destroy()
        return False
    
    # 检查必要的目录
    required_dirs = ['gui', 'configs', 'logs', 'models', 'results']
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
    
    return True

def create_splash_screen():
    """创建启动画面"""
    splash = tk.Toplevel()
    splash.title("电动汽车充电负荷预测系统")
    splash.geometry("500x300")
    splash.resizable(False, False)
    
    # 居中显示
    splash.update_idletasks()
    x = (splash.winfo_screenwidth() - splash.winfo_width()) // 2
    y = (splash.winfo_screenheight() - splash.winfo_height()) // 2
    splash.geometry(f"+{x}+{y}")
    
    # 移除窗口装饰
    splash.overrideredirect(True)
    
    # 创建内容
    main_frame = tk.Frame(splash, bg='#f0f0f0', relief='raised', bd=2)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = tk.Label(main_frame, 
                          text="电动汽车充电负荷预测系统",
                          font=("Microsoft YaHei", 18, "bold"),
                          bg='#f0f0f0', fg='#2c3e50')
    title_label.pack(pady=(40, 10))
    
    # 副标题
    subtitle_label = tk.Label(main_frame,
                             text="EV Charging Load Prediction System",
                             font=("Arial", 12),
                             bg='#f0f0f0', fg='#7f8c8d')
    subtitle_label.pack(pady=(0, 10))
    
    # 增强功能说明
    features_label = tk.Label(main_frame,
                             text="🎯 智能训练 • 📊 实时监控 • 🔄 日志同步 • 📈 可视化进度",
                             font=("Microsoft YaHei", 10),
                             bg='#f0f0f0', fg='#3498db')
    features_label.pack(pady=(0, 10))
    
    # 版本信息
    version_label = tk.Label(main_frame,
                            text="版本 2.0.0 Advanced",
                            font=("Microsoft YaHei", 10),
                            bg='#f0f0f0', fg='#95a5a6')
    version_label.pack(pady=(0, 10))
    
    # 加载信息
    loading_label = tk.Label(main_frame,
                            text="正在加载...",
                            font=("Microsoft YaHei", 10),
                            bg='#f0f0f0', fg='#3498db')
    loading_label.pack(pady=(20, 10))
    
    # 进度条（简单的动画）
    progress_frame = tk.Frame(main_frame, bg='#f0f0f0')
    progress_frame.pack(pady=(0, 40))
    
    progress_canvas = tk.Canvas(progress_frame, width=300, height=20, 
                               bg='#ecf0f1', highlightthickness=0)
    progress_canvas.pack()
    
    # 绘制进度条背景
    progress_canvas.create_rectangle(0, 0, 300, 20, fill='#bdc3c7', outline='')
    
    # 动画进度条
    progress_bar = progress_canvas.create_rectangle(0, 0, 0, 20, fill='#3498db', outline='')
    
    def animate_progress():
        try:
            for i in range(301):
                if not splash.winfo_exists():
                    break
                progress_canvas.coords(progress_bar, 0, 0, i, 20)
                splash.update()
                time.sleep(0.01)  # 10ms延迟
        except tk.TclError:
            # 窗口已被销毁，忽略错误
            pass

    # 启动动画
    splash.after(100, animate_progress)
    
    return splash

def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("启动电动汽车充电负荷预测系统GUI")
        
        # 检查系统要求
        if not check_system_requirements():
            return 1
        
        # 检查依赖包
        if not check_dependencies():
            return 1
        
        # 创建主窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建启动画面
        splash = create_splash_screen()
        
        # 等待启动画面完成
        root.after(3000, splash.destroy)
        root.wait_window(splash)
        
        # 导入GUI模块
        logger.info("导入GUI模块...")
        try:
            from gui.main_app import EVChargingPredictionGUI
        except ImportError as e:
            logger.error(f"导入GUI模块失败: {e}")
            messagebox.showerror("导入错误", f"无法导入GUI模块: {e}")
            return 1
        
        # 创建并启动GUI应用
        logger.info("创建GUI应用...")
        try:
            # EVChargingPredictionGUI类内部创建自己的root窗口
            app = EVChargingPredictionGUI()

            # 关闭启动画面的root窗口
            root.destroy()

            # 设置窗口关闭处理
            def on_closing():
                if messagebox.askokcancel("退出", "确定要退出系统吗？"):
                    logger.info("用户退出系统")
                    app.root.destroy()

            app.root.protocol("WM_DELETE_WINDOW", on_closing)
            
            logger.info("GUI应用启动成功")

            # 启动主循环
            app.root.mainloop()
            
        except Exception as e:
            logger.error(f"GUI应用启动失败: {e}")
            logger.error(traceback.format_exc())
            messagebox.showerror("启动错误", f"GUI应用启动失败:\n{e}")
            return 1
        
        logger.info("GUI应用正常退出")
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 1
    except Exception as e:
        print(f"程序异常退出: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    # 设置工作目录为脚本所在目录
    os.chdir(project_root)
    
    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)
