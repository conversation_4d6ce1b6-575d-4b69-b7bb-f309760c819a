"""
GUI工具函数
GUI Utility Functions
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
import threading
import time
import platform
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import warnings
import os
import sys

# 抑制matplotlib字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

# 全局字体配置
class FontManager:
    """字体管理器 - 统一管理GUI和matplotlib字体"""

    def __init__(self):
        self.gui_fonts = {}
        self.available_chinese_fonts = []
        self.default_font_family = None
        self.matplotlib_font = None
        self.gui_font = None
        self._detect_fonts()

    def _detect_fonts(self):
        """检测系统可用的中文字体"""
        try:
            # Windows系统字体
            if platform.system() == "Windows":
                candidate_fonts = [
                    'Microsoft YaHei',
                    'SimHei',
                    'SimSun',
                    'KaiTi',
                    'FangSong',
                    'Microsoft JhengHei'
                ]
            # macOS系统字体
            elif platform.system() == "Darwin":
                candidate_fonts = [
                    'PingFang SC',
                    'Heiti SC',
                    'STHeiti',
                    'Arial Unicode MS',
                    'Hiragino Sans GB'
                ]
            # Linux系统字体
            else:
                candidate_fonts = [
                    'WenQuanYi Micro Hei',
                    'WenQuanYi Zen Hei',
                    'Noto Sans CJK SC',
                    'Source Han Sans CN',
                    'Droid Sans Fallback'
                ]

            # 检测可用字体 - 延迟到需要时再检测
            self.candidate_fonts = candidate_fonts

            # 设置默认字体族（不创建字体对象）
            if platform.system() == "Windows":
                self.default_font_family = 'Microsoft YaHei'
            elif platform.system() == "Darwin":
                self.default_font_family = 'PingFang SC'
            else:
                self.default_font_family = 'WenQuanYi Micro Hei'

            # 设置matplotlib和GUI字体
            self.matplotlib_font = self.default_font_family
            self.gui_font = self.default_font_family

            print(f"✅ 默认GUI字体族: {self.default_font_family}")

        except Exception as e:
            print(f"⚠️ 字体检测失败: {e}")
            self.default_font_family = "TkDefaultFont"
            self.candidate_fonts = []
            self.matplotlib_font = "Microsoft YaHei"
            self.gui_font = "TkDefaultFont"

    def _ensure_fonts_detected(self):
        """确保字体已被检测"""
        if not hasattr(self, 'available_chinese_fonts'):
            self.available_chinese_fonts = []
            try:
                # 检测可用字体
                available_fonts = tkFont.families()
                for font in getattr(self, 'candidate_fonts', []):
                    if font in available_fonts:
                        self.available_chinese_fonts.append(font)

                # 更新默认字体
                if self.available_chinese_fonts:
                    self.default_font_family = self.available_chinese_fonts[0]

                print(f"✅ 检测到可用中文字体: {self.available_chinese_fonts}")
            except Exception as e:
                print(f"⚠️ 延迟字体检测失败: {e}")

    def get_font(self, size=9, weight="normal", style="roman"):
        """获取指定大小和样式的字体对象"""
        # 确保字体已检测
        self._ensure_fonts_detected()

        font_key = f"{size}_{weight}_{style}"

        if font_key not in self.gui_fonts:
            try:
                # 修正style参数
                slant = "italic" if style == "italic" else "roman"

                self.gui_fonts[font_key] = tkFont.Font(
                    family=self.default_font_family,
                    size=size,
                    weight=weight,
                    slant=slant
                )
            except Exception as e:
                print(f"⚠️ 创建字体失败: {e}")
                # 回退到默认字体
                try:
                    self.gui_fonts[font_key] = tkFont.nametofont("TkDefaultFont")
                except:
                    # 如果连默认字体都获取不到，返回None
                    return None

        return self.gui_fonts[font_key]

    def get_default_font(self):
        """获取默认字体"""
        return self.get_font(size=9)

    def get_title_font(self):
        """获取标题字体"""
        return self.get_font(size=12, weight="bold")

    def get_label_font(self):
        """获取标签字体"""
        return self.get_font(size=9)

    def get_button_font(self):
        """获取按钮字体"""
        return self.get_font(size=9)

    def get_text_font(self):
        """获取文本字体"""
        return self.get_font(size=9)

# 全局字体管理器实例
_font_manager = None

def get_font_manager():
    """获取全局字体管理器"""
    global _font_manager
    if _font_manager is None:
        _font_manager = FontManager()
    return _font_manager

def setup_gui_fonts():
    """设置GUI字体支持"""
    try:
        # 检查是否有根窗口
        if not hasattr(tk, '_default_root') or tk._default_root is None:
            print("⚠️ 没有根窗口，跳过GUI字体配置")
            return False

        font_manager = get_font_manager()

        # 配置tkinter默认字体
        default_font = tkFont.nametofont("TkDefaultFont")
        default_font.configure(family=font_manager.default_font_family, size=9)

        text_font = tkFont.nametofont("TkTextFont")
        text_font.configure(family=font_manager.default_font_family, size=9)

        fixed_font = tkFont.nametofont("TkFixedFont")
        fixed_font.configure(family=font_manager.default_font_family, size=9)

        print(f"✅ GUI字体配置成功")
        return True

    except Exception as e:
        print(f"⚠️ GUI字体配置失败: {e}")
        return False

def setup_matplotlib_fonts():
    """设置matplotlib中文字体支持"""
    try:
        # 检测系统可用的中文字体
        available_fonts = []

        # Windows系统字体
        if platform.system() == "Windows":
            windows_fonts = [
                'Microsoft YaHei',
                'SimHei',
                'SimSun',
                'KaiTi',
                'FangSong'
            ]
            for font in windows_fonts:
                if any(font in f.name for f in fm.fontManager.ttflist):
                    available_fonts.append(font)

        # macOS系统字体
        elif platform.system() == "Darwin":
            mac_fonts = [
                'PingFang SC',
                'Heiti SC',
                'STHeiti',
                'Arial Unicode MS'
            ]
            for font in mac_fonts:
                if any(font in f.name for f in fm.fontManager.ttflist):
                    available_fonts.append(font)

        # Linux系统字体
        else:
            linux_fonts = [
                'WenQuanYi Micro Hei',
                'WenQuanYi Zen Hei',
                'Noto Sans CJK SC',
                'Source Han Sans CN'
            ]
            for font in linux_fonts:
                if any(font in f.name for f in fm.fontManager.ttflist):
                    available_fonts.append(font)

        # 添加通用回退字体
        fallback_fonts = ['DejaVu Sans', 'Arial', 'sans-serif']
        available_fonts.extend(fallback_fonts)

        # 设置matplotlib字体
        plt.rcParams['font.sans-serif'] = available_fonts
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.family'] = 'sans-serif'

        # 设置默认字体大小
        plt.rcParams['font.size'] = 10
        plt.rcParams['axes.titlesize'] = 12
        plt.rcParams['axes.labelsize'] = 10
        plt.rcParams['xtick.labelsize'] = 9
        plt.rcParams['ytick.labelsize'] = 9
        plt.rcParams['legend.fontsize'] = 9

        print(f"✅ matplotlib字体配置成功，可用中文字体: {available_fonts[:3]}")
        return True

    except Exception as e:
        print(f"⚠️ matplotlib字体配置失败: {e}")
        # 使用最基本的配置
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['axes.unicode_minus'] = False
        return False

def setup_all_fonts():
    """设置所有字体支持（GUI + matplotlib）"""
    print("🔧 正在配置字体系统...")

    gui_success = setup_gui_fonts()
    matplotlib_success = setup_matplotlib_fonts()

    if gui_success and matplotlib_success:
        print("✅ 字体系统配置完成")
        return True
    else:
        print("⚠️ 字体系统配置部分失败")
        return False


class ToolTip:
    """工具提示类"""
    
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        
        self.widget.bind("<Enter>", self.on_enter)
        self.widget.bind("<Leave>", self.on_leave)
        
    def on_enter(self, event=None):
        """鼠标进入时显示提示"""
        if self.tooltip_window or not self.text:
            return
            
        x, y, _, _ = self.widget.bbox("insert") if hasattr(self.widget, 'bbox') else (0, 0, 0, 0)
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25
        
        self.tooltip_window = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f"+{x}+{y}")
        
        label = tk.Label(tw, text=self.text, justify='left',
                        background="#ffffe0", relief='solid', borderwidth=1,
                        font=("Arial", 9))
        label.pack(ipadx=1)
        
    def on_leave(self, event=None):
        """鼠标离开时隐藏提示"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None


def create_tooltip(widget, text):
    """为控件创建工具提示"""
    return ToolTip(widget, text)


def center_window(window, width=None, height=None):
    """将窗口居中显示"""
    window.update_idletasks()
    
    if width is None:
        width = window.winfo_reqwidth()
    if height is None:
        height = window.winfo_reqheight()
    
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    window.geometry(f"{width}x{height}+{x}+{y}")


class ProgressDialog:
    """进度对话框类"""
    
    def __init__(self, parent, title="处理中...", message="请稍候..."):
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 创建界面
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 消息标签
        self.message_var = tk.StringVar(value=message)
        ttk.Label(main_frame, textvariable=self.message_var, 
                 font=("Arial", 10)).pack(pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, 
                                          variable=self.progress_var,
                                          maximum=100, length=300)
        self.progress_bar.pack(pady=(0, 10))
        
        # 详细信息
        self.detail_var = tk.StringVar(value="")
        self.detail_label = ttk.Label(main_frame, textvariable=self.detail_var,
                                     font=("Arial", 8), foreground="gray")
        self.detail_label.pack()
        
        # 取消按钮
        self.cancelled = False
        self.cancel_button = ttk.Button(main_frame, text="取消", 
                                       command=self.cancel)
        self.cancel_button.pack(pady=(10, 0))
        
        # 居中显示
        center_window(self.dialog, 400, 150)
        
    def update_progress(self, value, message=None, detail=None):
        """更新进度"""
        self.progress_var.set(value)
        
        if message:
            self.message_var.set(message)
            
        if detail:
            self.detail_var.set(detail)
            
        self.dialog.update()
        
    def cancel(self):
        """取消操作"""
        self.cancelled = True
        
    def close(self):
        """关闭对话框"""
        self.dialog.destroy()
        
    def is_cancelled(self):
        """检查是否被取消"""
        return self.cancelled


def show_progress_dialog(parent, title="处理中...", message="请稍候..."):
    """显示进度对话框"""
    return ProgressDialog(parent, title, message)


class ScrollableFrame:
    """可滚动框架类"""
    
    def __init__(self, parent, **kwargs):
        self.outer_frame = ttk.Frame(parent, **kwargs)
        
        # 创建画布和滚动条
        self.canvas = tk.Canvas(self.outer_frame, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.outer_frame, orient="vertical", 
                                      command=self.canvas.yview)
        
        # 创建内部框架
        self.inner_frame = ttk.Frame(self.canvas)
        
        # 配置滚动
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        self.canvas_frame = self.canvas.create_window((0, 0), 
                                                     window=self.inner_frame, 
                                                     anchor="nw")
        
        # 绑定事件
        self.inner_frame.bind("<Configure>", self.on_frame_configure)
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)
        
        # 布局
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
    def on_frame_configure(self, event):
        """内部框架大小改变时更新滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def on_canvas_configure(self, event):
        """画布大小改变时更新内部框架宽度"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_frame, width=canvas_width)
        
    def on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
    def get_frame(self):
        """获取外部框架"""
        return self.outer_frame
        
    def get_inner_frame(self):
        """获取内部框架"""
        return self.inner_frame


def create_scrollable_frame(parent, **kwargs):
    """创建可滚动框架"""
    return ScrollableFrame(parent, **kwargs)


class ThemeManager:
    """主题管理器"""
    
    THEMES = {
        'light': {
            'bg': '#ffffff',
            'fg': '#000000',
            'select_bg': '#0078d4',
            'select_fg': '#ffffff',
            'entry_bg': '#ffffff',
            'entry_fg': '#000000',
            'button_bg': '#f0f0f0',
            'button_fg': '#000000'
        },
        'dark': {
            'bg': '#2d2d2d',
            'fg': '#ffffff',
            'select_bg': '#404040',
            'select_fg': '#ffffff',
            'entry_bg': '#404040',
            'entry_fg': '#ffffff',
            'button_bg': '#505050',
            'button_fg': '#ffffff'
        }
    }
    
    @classmethod
    def apply_theme(cls, root, theme_name='light'):
        """应用主题"""
        if theme_name not in cls.THEMES:
            theme_name = 'light'
            
        theme = cls.THEMES[theme_name]
        
        # 配置ttk样式
        style = ttk.Style()
        
        # 设置主题
        if theme_name == 'dark':
            style.theme_use('clam')
        else:
            style.theme_use('default')
            
        # 配置各种控件样式
        style.configure('TLabel', 
                       background=theme['bg'], 
                       foreground=theme['fg'])
        
        style.configure('TFrame', 
                       background=theme['bg'])
        
        style.configure('TButton',
                       background=theme['button_bg'],
                       foreground=theme['button_fg'])
        
        style.configure('TEntry',
                       fieldbackground=theme['entry_bg'],
                       foreground=theme['entry_fg'])
        
        style.configure('TCombobox',
                       fieldbackground=theme['entry_bg'],
                       foreground=theme['entry_fg'])
        
        style.configure('Treeview',
                       background=theme['bg'],
                       foreground=theme['fg'],
                       fieldbackground=theme['bg'])
        
        style.configure('Treeview.Heading',
                       background=theme['button_bg'],
                       foreground=theme['button_fg'])
        
        # 配置根窗口
        root.configure(bg=theme['bg'])


def apply_theme(root, theme_name='light'):
    """应用主题"""
    ThemeManager.apply_theme(root, theme_name)


def create_separator(parent, orient='horizontal', **kwargs):
    """创建分隔符"""
    return ttk.Separator(parent, orient=orient, **kwargs)


def create_labeled_entry(parent, label_text, entry_var=None, **kwargs):
    """创建带标签的输入框"""
    frame = ttk.Frame(parent)
    
    label = ttk.Label(frame, text=label_text)
    label.pack(side=tk.LEFT, padx=(0, 10))
    
    if entry_var is None:
        entry_var = tk.StringVar(master=frame)
    
    entry = ttk.Entry(frame, textvariable=entry_var, **kwargs)
    entry.pack(side=tk.RIGHT, fill=tk.X, expand=True)
    
    return frame, entry_var, entry


def create_labeled_combobox(parent, label_text, values, combo_var=None, **kwargs):
    """创建带标签的下拉框"""
    frame = ttk.Frame(parent)
    
    label = ttk.Label(frame, text=label_text)
    label.pack(side=tk.LEFT, padx=(0, 10))
    
    if combo_var is None:
        combo_var = tk.StringVar(master=frame)
    
    combo = ttk.Combobox(frame, textvariable=combo_var, values=values, 
                        state="readonly", **kwargs)
    combo.pack(side=tk.RIGHT, fill=tk.X, expand=True)
    
    return frame, combo_var, combo


def create_button_frame(parent, buttons_config, **kwargs):
    """创建按钮框架
    
    buttons_config: [(text, command), ...]
    """
    frame = ttk.Frame(parent, **kwargs)
    
    buttons = []
    for text, command in buttons_config:
        button = ttk.Button(frame, text=text, command=command)
        button.pack(side=tk.LEFT, padx=(0, 5))
        buttons.append(button)
    
    return frame, buttons


def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def format_duration(seconds):
    """格式化时间长度"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"


def safe_float(value, default=0.0):
    """安全转换为浮点数"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value, default=0):
    """安全转换为整数"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def truncate_text(text, max_length=50):
    """截断文本"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


class StatusBar:
    """状态栏类"""
    
    def __init__(self, parent):
        self.frame = ttk.Frame(parent)
        
        # 状态文本
        self.status_var = tk.StringVar(master=self.frame, value="就绪")
        self.status_label = ttk.Label(self.frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 分隔符
        ttk.Separator(self.frame, orient='vertical').pack(side=tk.LEFT,
                                                         fill=tk.Y, padx=5)

        # 进度条
        self.progress_var = tk.DoubleVar(master=self.frame)
        self.progress_bar = ttk.Progressbar(self.frame,
                                          variable=self.progress_var,
                                          length=200, height=15)
        self.progress_bar.pack(side=tk.RIGHT, padx=(0, 5))

        # 进度文本
        self.progress_text_var = tk.StringVar(master=self.frame)
        self.progress_label = ttk.Label(self.frame, textvariable=self.progress_text_var,
                                       font=("Arial", 8))
        self.progress_label.pack(side=tk.RIGHT, padx=(0, 5))
        
    def set_status(self, text):
        """设置状态文本"""
        self.status_var.set(text)
        
    def set_progress(self, value, text=""):
        """设置进度"""
        self.progress_var.set(value)
        self.progress_text_var.set(text)
        
    def get_frame(self):
        """获取状态栏框架"""
        return self.frame


def create_status_bar(parent):
    """创建状态栏"""
    return StatusBar(parent)


def setup_style():
    """设置GUI样式"""
    # 首先设置matplotlib字体
    setup_matplotlib_fonts()

    style = ttk.Style()

    # 设置默认主题
    try:
        style.theme_use('clam')
    except tk.TclError:
        style.theme_use('default')

    # 配置按钮样式
    style.configure('Accent.TButton',
                   background='#0078d4',
                   foreground='white',
                   borderwidth=0,
                   focuscolor='none')

    style.map('Accent.TButton',
              background=[('active', '#106ebe'),
                         ('pressed', '#005a9e')])

    # 配置标签框样式
    style.configure('Card.TLabelFrame',
                   background='#f8f9fa',
                   borderwidth=1,
                   relief='solid')

    # 配置树形视图样式
    style.configure('Treeview',
                   background='white',
                   foreground='black',
                   rowheight=25,
                   fieldbackground='white')

    style.configure('Treeview.Heading',
                   background='#e9ecef',
                   foreground='black',
                   relief='flat')

    style.map('Treeview',
              background=[('selected', '#0078d4')],
              foreground=[('selected', 'white')])

    return style
