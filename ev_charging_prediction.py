import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler
import random
from datetime import datetime, timedelta
import chinese_calendar
from vmdpy import VMD
import warnings
import os
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from sklearn.metrics import r2_score
import concurrent.futures
from multiprocessing import Manager, cpu_count, shared_memory, Queue
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import threading
import queue
import psutil
import gc
import time
from threading import Lock
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)
random.seed(42)

# ---------------- 新增: 全局设备与默认张量类型 ----------------
def get_device_info():
    """获取设备信息和兼容性检查"""
    if not torch.cuda.is_available():
        return torch.device('cpu'), 'cpu', 0

    device = torch.device('cuda')
    gpu_name = torch.cuda.get_device_name(0)
    compute_capability = torch.cuda.get_device_capability(0)

    # 计算能力检查
    major, minor = compute_capability
    sm_version = major * 10 + minor

    print(f"检测到GPU: {gpu_name}")
    print(f"计算能力: sm_{sm_version} (CUDA {major}.{minor})")

    # 兼容性警告
    if sm_version < 80:
        print(f"⚠️  警告: 您的GPU (sm_{sm_version}) 不支持Flash Attention")
        print("   将使用兼容的注意力机制，性能可能略有下降")

    return device, gpu_name, sm_version

DEVICE, GPU_NAME, SM_VERSION = get_device_info()

# 根据GPU能力设置优化选项
if DEVICE.type == 'cuda' and SM_VERSION >= 70:
    torch.backends.cudnn.benchmark = True
    print("✅ 启用CUDNN优化")
else:
    print("ℹ️  使用基础CUDA设置")
# -----------------------------------------------------------

# ============== 并行处理和资源管理 ==============

class GPUResourceManager:
    """GPU资源池管理器，支持多进程安全的GPU资源分配"""

    def __init__(self):
        self.lock = Lock()
        self.gpu_available = torch.cuda.is_available()
        self.gpu_count = torch.cuda.device_count() if self.gpu_available else 0
        self.memory_per_process = self._calculate_memory_per_process()
        self.process_gpu_map = {}

    def _calculate_memory_per_process(self):
        """计算每个进程可用的GPU内存"""
        if not self.gpu_available:
            return 0

        total_memory = torch.cuda.get_device_properties(0).total_memory
        # 保留20%内存作为缓冲，其余平均分配给进程
        usable_memory = total_memory * 0.8
        max_processes = min(cpu_count(), 4)  # 限制最大进程数
        return int(usable_memory / max_processes)

    def allocate_gpu_memory(self, process_id, memory_fraction=0.8):
        """为进程分配GPU内存"""
        if not self.gpu_available:
            return False

        try:
            # 设置GPU内存增长策略
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                # 限制内存使用
                torch.cuda.set_per_process_memory_fraction(memory_fraction)
            return True
        except Exception as e:
            print(f"GPU内存分配失败 (进程 {process_id}): {e}")
            return False

    def release_gpu_memory(self, process_id):
        """释放进程的GPU内存"""
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                gc.collect()
        except Exception as e:
            print(f"GPU内存释放失败 (进程 {process_id}): {e}")

class PerformanceMonitor:
    """性能监控器，跟踪并行训练的性能指标"""

    def __init__(self):
        self.lock = Lock()
        self.start_time = None
        self.task_times = []
        self.gpu_usage = []
        self.memory_usage = []
        self.completed_tasks = 0
        self.total_tasks = 0

    def start_monitoring(self, total_tasks):
        """开始监控"""
        with self.lock:
            self.start_time = time.time()
            self.total_tasks = total_tasks
            self.completed_tasks = 0
            self.task_times.clear()
            self.gpu_usage.clear()
            self.memory_usage.clear()

    def record_task_completion(self, task_time, gpu_memory_used=None):
        """记录任务完成"""
        with self.lock:
            self.completed_tasks += 1
            self.task_times.append(task_time)

            if gpu_memory_used:
                self.memory_usage.append(gpu_memory_used)

    def get_progress_info(self):
        """获取进度信息"""
        with self.lock:
            if self.start_time is None:
                return "监控未开始"

            elapsed = time.time() - self.start_time
            progress = self.completed_tasks / self.total_tasks if self.total_tasks > 0 else 0

            if self.completed_tasks > 0:
                avg_task_time = np.mean(self.task_times)
                remaining_tasks = self.total_tasks - self.completed_tasks
                eta = remaining_tasks * avg_task_time

                return {
                    'progress': progress,
                    'completed': self.completed_tasks,
                    'total': self.total_tasks,
                    'elapsed': elapsed,
                    'eta': eta,
                    'avg_task_time': avg_task_time,
                    'tasks_per_second': self.completed_tasks / elapsed if elapsed > 0 else 0
                }

            return {
                'progress': progress,
                'completed': self.completed_tasks,
                'total': self.total_tasks,
                'elapsed': elapsed
            }

class SharedDataManager:
    """共享数据管理器，减少进程间数据传输开销"""

    def __init__(self):
        self.manager = None
        self.shared_data = None
        self.data_cache = {}
        self._initialized = False

    def _ensure_initialized(self):
        """延迟初始化Manager，避免Windows多进程问题"""
        if not self._initialized:
            self.manager = Manager()
            self.shared_data = self.manager.dict()
            self._initialized = True

    def store_training_data(self, key, X_train, y_train, X_val, y_val):
        """存储训练数据到共享内存"""
        try:
            self._ensure_initialized()
            # 将数据转换为numpy数组并存储
            self.shared_data[f"{key}_X_train"] = X_train
            self.shared_data[f"{key}_y_train"] = y_train
            self.shared_data[f"{key}_X_val"] = X_val
            self.shared_data[f"{key}_y_val"] = y_val
            return True
        except Exception as e:
            print(f"存储训练数据失败: {e}")
            return False

    def get_training_data(self, key):
        """从共享内存获取训练数据"""
        try:
            self._ensure_initialized()
            X_train = self.shared_data[f"{key}_X_train"]
            y_train = self.shared_data[f"{key}_y_train"]
            X_val = self.shared_data[f"{key}_X_val"]
            y_val = self.shared_data[f"{key}_y_val"]
            return X_train, y_train, X_val, y_val
        except KeyError:
            return None
        except Exception as e:
            print(f"获取训练数据失败: {e}")
            return None

    def cleanup(self):
        """清理共享数据"""
        try:
            if self.shared_data:
                self.shared_data.clear()
        except Exception as e:
            print(f"清理共享数据失败: {e}")

# 全局资源管理器实例（延迟初始化）
gpu_manager = None
performance_monitor = None
shared_data_manager = None

def get_global_managers():
    """获取全局管理器实例（延迟初始化）"""
    global gpu_manager, performance_monitor, shared_data_manager

    if gpu_manager is None:
        gpu_manager = GPUResourceManager()
    if performance_monitor is None:
        performance_monitor = PerformanceMonitor()
    if shared_data_manager is None:
        shared_data_manager = SharedDataManager()

    return gpu_manager, performance_monitor, shared_data_manager

# -----------------------------------------------------------

def parallel_fitness_evaluator(args):
    """并行适应度评估函数，用于多进程执行"""
    solution, train_data, val_data, input_size, output_size, process_id = args

    start_time = time.time()

    try:
        # 获取管理器实例
        gpu_mgr, perf_monitor, _ = get_global_managers()

        # 分配GPU资源
        gpu_allocated = gpu_mgr.allocate_gpu_memory(process_id)
        device = torch.device('cuda' if gpu_allocated else 'cpu')

        # 创建模型
        model = SSAVMDGRU(
            input_size=input_size,
            hidden_size=int(solution['hidden_size']),
            num_layers=int(solution['num_layers']),
            output_size=output_size,
            dropout=solution['dropout'],
            loss_type=solution['loss_type'],
            weight_alpha=solution.get('alpha', 1.0),
            model_type=solution.get('model_type', 'gru')
        )

        # 准备数据
        X_train, y_train = train_data
        X_val, y_val = val_data

        # 创建数据加载器
        train_dataset = torch.utils.data.TensorDataset(
            torch.FloatTensor(X_train), torch.FloatTensor(y_train)
        )
        val_dataset = torch.utils.data.TensorDataset(
            torch.FloatTensor(X_val), torch.FloatTensor(y_val)
        )

        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=int(solution['batch_size']), shuffle=True
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset, batch_size=int(solution['batch_size']), shuffle=False
        )

        # 训练模型（使用更短的训练时间）
        model.train(train_loader, val_loader, num_epochs=5, patience=3)

        # 评估性能
        model.gru_model.eval()
        val_predictions = []
        val_targets = []

        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                X_batch = X_batch.to(device)
                y_batch = y_batch.to(device)

                outputs = model.gru_model(X_batch)
                val_predictions.extend(outputs.cpu().numpy().flatten())
                val_targets.extend(y_batch.cpu().numpy().flatten())

        # 计算R²作为适应度
        fitness = r2_score(val_targets, val_predictions)

        # 记录性能
        task_time = time.time() - start_time
        gpu_memory = torch.cuda.max_memory_allocated() / 1024**2 if torch.cuda.is_available() else 0
        perf_monitor.record_task_completion(task_time, gpu_memory)

        # 释放GPU资源
        gpu_mgr.release_gpu_memory(process_id)

        return fitness

    except Exception as e:
        print(f"进程 {process_id} 适应度评估失败: {e}")
        gpu_mgr, _, _ = get_global_managers()
        gpu_mgr.release_gpu_memory(process_id)
        return -1.0  # 返回最差适应度

class ParallelEvaluator:
    """并行适应度评估器"""

    def __init__(self, n_workers=None, use_threading=False):
        self.n_workers = n_workers or min(cpu_count(), 4)  # 限制最大进程数
        self.use_threading = use_threading
        self.executor = None

    def __enter__(self):
        if self.use_threading:
            self.executor = ThreadPoolExecutor(max_workers=self.n_workers)
        else:
            self.executor = ProcessPoolExecutor(max_workers=self.n_workers)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.executor:
            self.executor.shutdown(wait=True)

    def evaluate_population(self, population, train_data, val_data, input_size, output_size):
        """并行评估整个种群的适应度"""

        # 获取性能监控器并开始监控
        _, perf_monitor, _ = get_global_managers()
        perf_monitor.start_monitoring(len(population))

        # 准备任务参数
        tasks = []
        for i, solution in enumerate(population):
            task_args = (solution, train_data, val_data, input_size, output_size, i)
            tasks.append(task_args)

        # 提交任务并收集结果
        fitness_values = []

        if self.use_threading:
            # 使用线程池（适合I/O密集型任务）
            futures = [self.executor.submit(parallel_fitness_evaluator, task) for task in tasks]

            for future in as_completed(futures):
                try:
                    fitness = future.result(timeout=300)  # 5分钟超时
                    fitness_values.append(fitness)
                except Exception as e:
                    print(f"任务执行失败: {e}")
                    fitness_values.append(-1.0)
        else:
            # 使用进程池（适合CPU密集型任务）
            try:
                fitness_values = list(self.executor.map(parallel_fitness_evaluator, tasks, timeout=300))
            except Exception as e:
                print(f"批量任务执行失败: {e}")
                fitness_values = [-1.0] * len(population)

        return fitness_values

    def evaluate_batch(self, solutions, train_data, val_data, input_size, output_size):
        """评估一批解决方案（用于更细粒度的控制）"""

        tasks = []
        for i, solution in enumerate(solutions):
            task_args = (solution, train_data, val_data, input_size, output_size, i)
            tasks.append(task_args)

        # 使用map进行批量处理
        try:
            fitness_values = list(self.executor.map(parallel_fitness_evaluator, tasks))
        except Exception as e:
            print(f"批量评估失败: {e}")
            fitness_values = [-1.0] * len(solutions)

        return fitness_values

# -----------------------------------------------------------

def safe_array(arr, fill=0):
    arr = np.array(arr)
    arr[np.isnan(arr)] = fill
    arr[np.isinf(arr)] = fill
    return arr

def is_holiday(date):
    """
    判断给定日期是否为节假日
    返回：1表示节假日，0表示工作日
    """
    try:
        return 1 if chinese_calendar.is_holiday(date) else 0
    except:
        # 如果chinese_calendar无法判断，使用简单的周末判断
        return 1 if date.weekday() >= 5 else 0



class DataPreprocessor:
    def __init__(self):
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        self.load_scaler = MinMaxScaler(feature_range=(0, 1))
        self.categorical_encoders = {}
        self.feature_columns = []
        self.numeric_columns = []
    
    def load_and_clean_data(self, file_path):
        df = pd.read_csv(file_path)
        
        # ----------------- 新增: 统一列名映射 -----------------
        # 针对不同数据源建立更全面的列名映射表, 若不存在则保持原名
        rename_map = {
            # 旧版列名 → 统一列名
            'Charging_Load_kW': 'charging_load',
            'Charging_Duration_hours': 'charging_duration',
            'Temperature_C': 'temperature',
            'Humidity_%': 'humidity',
            'Timestamp': 'timestamp',
            # 新版数据集列名 → 统一列名
            'start_time': 'timestamp',
            '总有功功率_总和(kW)': 'charging_load',
            '平均气温(℃)': 'temperature',
            '降水量(mm)': 'humidity',  # 用降水量近似湿度, 若不合适后续可调整
            'A相电压_均值(V)': 'avg_voltage',
            'A相电流_均值(A)': 'avg_current',
            '最低气温(℃)': 'temp_min',
            '最高气温(℃)': 'temp_max',
            '充电时间': 'timestamp'
        }
        df = df.rename(columns={k: v for k, v in rename_map.items() if k in df.columns})
        # ------------------------------------------------------
        
        # 转换时间戳
        if 'timestamp' not in df.columns:
            raise KeyError("数据集中缺少时间戳列, 请检查列名映射表。")
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # ----------------- 新增: 必要列占位 -----------------
        # 部分后续特征工程依赖于以下列, 若缺失则创建占位列(填 0)
        required_cols = ['humidity']
        for col in required_cols:
            if col not in df.columns:
                df[col] = 0.0
        # ------------------------------------------------------

        # 处理分类变量 (若存在)
        categorical_columns = ['Vehicle_Types', 'Charging_Preferences', 'EV_Usage_Patterns']
        for col in categorical_columns:
            if col in df.columns:
                df[col] = df[col].astype('category')
                self.categorical_encoders[col] = pd.get_dummies(df[col], prefix=col)
                df = pd.concat([df, self.categorical_encoders[col]], axis=1)
                df = df.drop(col, axis=1)
        
        # 填充缺失值
        df = self.fill_missing_values(df)
        
        # 处理异常值
        df = self.handle_outliers(df)
        
        return df

    def load_and_clean_data_from_df(self, df):
        """从DataFrame加载和清洗数据的适配方法"""
        # 复制DataFrame以避免修改原始数据
        df = df.copy()

        # 列名映射 - 与原始load_and_clean_data方法保持一致
        rename_map = {
            # 旧版列名 → 统一列名
            'Charging_Load_kW': 'charging_load',
            'Charging_Duration_hours': 'charging_duration',
            'Temperature_C': 'temperature',
            'Humidity_%': 'humidity',
            'Timestamp': 'timestamp',
            # 新版数据集列名 → 统一列名
            'start_time': 'timestamp',
            '总有功功率_总和(kW)': 'charging_load',
            '平均气温(℃)': 'temperature',
            '降水量(mm)': 'humidity',  # 用降水量近似湿度
            'A相电压_均值(V)': 'avg_voltage',
            'A相电流_均值(A)': 'avg_current',
            '最低气温(℃)': 'temp_min',
            '最高气温(℃)': 'temp_max',
            '充电时间': 'timestamp'
        }

        # 应用列名映射
        df = df.rename(columns={k: v for k, v in rename_map.items() if k in df.columns})

        # 检查必要的列
        if 'timestamp' not in df.columns:
            raise KeyError("数据集中缺少时间戳列, 请检查列名映射表。")

        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # 必要列占位 - 如果缺失则创建占位列
        required_cols = ['humidity']
        for col in required_cols:
            if col not in df.columns:
                df[col] = 0.0

        # 确保charging_load列存在
        if 'charging_load' not in df.columns:
            raise KeyError("数据集中缺少充电负荷列")

        # 数据清洗
        df = df.dropna(subset=['timestamp', 'charging_load'])

        # 按时间排序
        df = df.sort_values('timestamp').reset_index(drop=True)

        return df

    def fill_missing_values(self, df):
        """改进的缺失值处理，使用时间序列插值"""
        # 核心数值列
        numeric_columns = ['temperature', 'humidity', 'charging_load', 'avg_voltage', 'avg_current', 'temp_min', 'temp_max']

        for col in numeric_columns:
            if col in df.columns:
                # 首先尝试时间序列插值
                if df[col].isna().sum() > 0:
                    # 线性插值
                    df[col] = df[col].interpolate(method='linear', limit_direction='both')

                    # 如果仍有缺失值，使用前向填充
                    df[col] = df[col].fillna(method='ffill')

                    # 如果开头有缺失值，使用后向填充
                    df[col] = df[col].fillna(method='bfill')

                    # 最后的保险：使用中位数填充
                    df[col] = df[col].fillna(df[col].median())

                    # 添加缺失值指示器
                    df[f'{col}_was_missing'] = df[col].isna().astype(int)

        return df
    
    def handle_outliers(self, df):
        # 核心数值列
        numeric_columns = ['temperature', 'humidity', 'charging_load', 'avg_voltage', 'avg_current', 'temp_min', 'temp_max']

        for col in numeric_columns:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                df[col] = df[col].clip(lower_bound, upper_bound)

        return df
    
    def create_time_features(self, df):
        """
        生成所有时间序列特征。
        """
        df = self._create_time_features_independent(df)
        df = self._create_time_features_dependent(df)
        return df

    def _create_time_features_independent(self, df):
        """
        创建所有不依赖于历史序列的特征（仅基于时间戳和外生变量）。
        """
        # 基本时间特征
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['month'] = df['timestamp'].dt.month
        df['day'] = df['timestamp'].dt.day
        df['is_weekend'] = df['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)

        # 周期性时间特征
        df['hour_sin'] = np.sin(2 * np.pi * df['hour']/24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour']/24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_week']/7)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_week']/7)
        df['month_sin'] = np.sin(2 * np.pi * df['month']/12)
        df['month_cos'] = np.cos(2 * np.pi * df['month']/12)

        # 新增：更多周期性特征
        df['quarter'] = df['timestamp'].dt.quarter
        df['quarter_sin'] = np.sin(2 * np.pi * df['quarter']/4)
        df['quarter_cos'] = np.cos(2 * np.pi * df['quarter']/4)
        df['day_of_year'] = df['timestamp'].dt.dayofyear
        df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year']/365)
        df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year']/365)

        # 特殊时间特征
        df['is_month_start'] = df['timestamp'].dt.is_month_start.astype(int)
        df['is_month_end'] = df['timestamp'].dt.is_month_end.astype(int)
        df['is_quarter_start'] = df['timestamp'].dt.is_quarter_start.astype(int)
        df['is_quarter_end'] = df['timestamp'].dt.is_quarter_end.astype(int)

        # 时间段特征（更细粒度）
        df['time_of_day'] = df['hour'].apply(lambda h: (h % 24 + 4) // 4 % 4) # 0-夜, 1-早, 2-午, 3-晚
        df['hour_group'] = df['hour'].apply(lambda x: 0 if x < 6 else (1 if x < 12 else (2 if x < 18 else 3)))
        df['is_peak_hour'] = df['hour'].apply(lambda x: 1 if 18 <= x <= 22 else 0)
        df['is_work_hour'] = df['hour'].apply(lambda x: 1 if 8 <= x <= 18 else 0)
        df['is_night'] = df['hour'].apply(lambda x: 1 if x < 6 or x > 22 else 0)

        # 简化节假日特征
        df['is_holiday'] = df['timestamp'].apply(is_holiday)

        # 新增：工作日类型特征
        df['workday_type'] = df.apply(lambda row: 0 if row['is_holiday'] else (1 if row['is_weekend'] else 2), axis=1)
        df['weekend_hour'] = df['is_weekend'] * df['hour']
        df['holiday_hour'] = df['is_holiday'] * df['hour']

        # 新增：零值感知特征
        df['is_charging'] = (df['charging_load'] > 1e-6).astype(int)
        df['charging_intensity'] = df['charging_load'] / (df['charging_load'].rolling(24, min_periods=1).mean() + 1e-6)

        # 增强的天气特征
        df['temp_humidity'] = df['temperature'] * df['humidity']
        if 'temp_min' in df.columns and 'temp_max' in df.columns:
            df['temp_range'] = df['temp_max'] - df['temp_min']
            df['temp_avg'] = (df['temp_max'] + df['temp_min']) / 2
            df['comfort_index'] = 1 / (1 + np.abs(df['temperature'] - 25) + df['humidity']/100)

        # 新增：傅里叶特征（捕获复杂周期性）
        for period in [24, 168, 720]:  # 日、周、月周期
            df[f'fourier_sin_{period}'] = np.sin(2 * np.pi * np.arange(len(df)) / period)
            df[f'fourier_cos_{period}'] = np.cos(2 * np.pi * np.arange(len(df)) / period)

        return df

    def _create_time_features_dependent(self, df):
        """
        创建依赖于历史序列的特征（滞后、滚动等）。
        """
        # 扩展的滞后特征
        for lag in [1, 2, 3, 6, 12, 24, 48, 168]:  # 添加48小时和一周滞后
            df[f'load_lag_{lag}'] = df['charging_load'].shift(lag)
            if lag <= 24:  # 温度滞后特征不需要太长
                df[f'temp_lag_{lag}'] = df['temperature'].shift(lag)

        # 扩展的滑动窗口特征
        for window in [3, 6, 12, 24, 48, 168]:  # 添加更多窗口大小
            df[f'load_rolling_mean_{window}'] = df['charging_load'].rolling(window=window, min_periods=1).mean()
            df[f'load_rolling_std_{window}'] = df['charging_load'].rolling(window=window, min_periods=1).std()
            df[f'load_rolling_max_{window}'] = df['charging_load'].rolling(window=window, min_periods=1).max()
            df[f'load_rolling_min_{window}'] = df['charging_load'].rolling(window=window, min_periods=1).min()

            if window <= 24:
                df[f'temp_rolling_mean_{window}'] = df['temperature'].rolling(window=window, min_periods=1).mean()
                df[f'temp_rolling_std_{window}'] = df['temperature'].rolling(window=window, min_periods=1).std()

        # 新增：差分特征
        for lag in [1, 24, 168]:
            df[f'load_diff_{lag}'] = df['charging_load'].diff(lag)
            if lag <= 24:
                df[f'temp_diff_{lag}'] = df['temperature'].diff(lag)

        # 新增：指数加权移动平均
        for span in [12, 24, 168]:
            df[f'load_ewm_{span}'] = df['charging_load'].ewm(span=span).mean()
            if span <= 24:
                df[f'temp_ewm_{span}'] = df['temperature'].ewm(span=span).mean()

        # 新增：分位数特征
        for window in [24, 168]:
            df[f'load_rolling_q25_{window}'] = df['charging_load'].rolling(window=window, min_periods=1).quantile(0.25)
            df[f'load_rolling_q75_{window}'] = df['charging_load'].rolling(window=window, min_periods=1).quantile(0.75)
            df[f'load_rolling_median_{window}'] = df['charging_load'].rolling(window=window, min_periods=1).median()

        # 新增：相对位置特征
        for window in [24, 168]:
            rolling_mean = df['charging_load'].rolling(window=window, min_periods=1).mean()
            rolling_std = df['charging_load'].rolling(window=window, min_periods=1).std()
            df[f'load_zscore_{window}'] = (df['charging_load'] - rolling_mean) / (rolling_std + 1e-6)

        return df



    def prepare_sequences(self, df, sequence_length=24):
        # One-hot encode time_of_day
        if 'time_of_day' in df.columns:
            df = pd.concat([df, pd.get_dummies(df['time_of_day'], prefix='time_of_day')], axis=1)

        # 核心特征列
        feature_columns = [
            'temperature', 'humidity', 'charging_load',
            'hour', 'day_of_week', 'month', 'is_weekend',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos',
            'month_sin', 'month_cos'
        ]

        # 添加新的时间特征
        new_time_features = [
            'quarter_sin', 'quarter_cos', 'day_of_year_sin', 'day_of_year_cos',
            'hour_group', 'is_peak_hour', 'is_work_hour', 'is_night',
            'workday_type', 'weekend_hour', 'holiday_hour'
        ]
        for feat in new_time_features:
            if feat in df.columns:
                feature_columns.append(feat)

        # 添加零值感知特征
        zero_aware_features = ['is_charging', 'charging_intensity']
        for feat in zero_aware_features:
            if feat in df.columns:
                feature_columns.append(feat)

        # 添加可用的电压电流特征
        if 'avg_voltage' in df.columns:
            feature_columns.append('avg_voltage')
        if 'avg_current' in df.columns:
            feature_columns.append('avg_current')
        if 'temp_min' in df.columns:
            feature_columns.append('temp_min')
        if 'temp_max' in df.columns:
            feature_columns.append('temp_max')

        # 添加增强的天气特征
        weather_features = ['temp_range', 'temp_avg', 'comfort_index']
        for feat in weather_features:
            if feat in df.columns:
                feature_columns.append(feat)

        # 添加滞后特征
        lag_features = [col for col in df.columns if col.startswith(('load_lag_', 'temp_lag_'))]
        feature_columns.extend(lag_features)

        # 添加滑动窗口特征
        rolling_features = [col for col in df.columns if col.startswith(('load_rolling_', 'temp_rolling_'))]
        feature_columns.extend(rolling_features)

        # 添加差分特征
        diff_features = [col for col in df.columns if col.startswith(('load_diff_', 'temp_diff_'))]
        feature_columns.extend(diff_features)

        # 添加指数加权移动平均特征
        ewm_features = [col for col in df.columns if col.startswith(('load_ewm_', 'temp_ewm_'))]
        feature_columns.extend(ewm_features)

        # 添加分位数特征
        quantile_features = [col for col in df.columns if col.startswith('load_rolling_q')]
        feature_columns.extend(quantile_features)

        # 添加Z-score特征
        zscore_features = [col for col in df.columns if col.startswith('load_zscore_')]
        feature_columns.extend(zscore_features)

        # 添加傅里叶特征
        fourier_features = [col for col in df.columns if col.startswith('fourier_')]
        feature_columns.extend(fourier_features)

        # 添加交互特征
        if 'temp_humidity' in df.columns:
            feature_columns.append('temp_humidity')

        # 添加时间特征
        time_of_day_features = [col for col in df.columns if col.startswith('time_of_day_')]
        feature_columns.extend(time_of_day_features)
        if 'is_holiday' in df.columns:
            feature_columns.append('is_holiday')

        # 添加缺失值指示器
        missing_indicators = [col for col in df.columns if col.endswith('_was_missing')]
        feature_columns.extend(missing_indicators)
        
        # 确保所有特征列都存在
        feature_columns = [col for col in feature_columns if col in df.columns]
        self.feature_columns = feature_columns
        
        # 分别对数值特征进行归一化
        numeric_columns = ['temperature', 'humidity']
        # 添加可用的数值特征
        for col in ['avg_voltage', 'avg_current', 'temp_min', 'temp_max']:
            if col in df.columns:
                numeric_columns.append(col)
        self.numeric_columns = numeric_columns
        
        if numeric_columns:
            df[numeric_columns] = self.scaler.fit_transform(df[numeric_columns])
        
        # 单独对充电负荷进行归一化并保存scaler
        if 'charging_load' in df.columns:
            df['charging_load'] = self.load_scaler.fit_transform(df[['charging_load']])
        
        # 处理缺失值
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(0)
        
        # 确保所有特征都是数值类型
        for col in feature_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 准备序列数据
        X, y, idx_list = [], [], []
        for i in range(len(df) - sequence_length):
            try:
                x_seq = df[feature_columns].iloc[i:i+sequence_length].values.astype(np.float32)
                y_val = df['charging_load'].iloc[i+sequence_length]
                
                # 检查是否有无效值
                if np.any(np.isnan(x_seq)) or np.any(np.isinf(x_seq)) or np.isnan(y_val) or np.isinf(y_val):
                    continue
                
                X.append(x_seq)
                y.append(y_val)
                idx_list.append(df['timestamp'].iloc[i+sequence_length])
            except Exception as e:
                print(f"Error processing sequence at index {i}: {str(e)}")
                continue
        
        if not X or not y:
            raise ValueError("No valid sequences could be created from the data")
        
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32), np.array(idx_list), self.load_scaler
    
    def split_data(self, X, y, idx_arr=None):
        train_size = int(len(X) * 0.7)
        val_size = int(len(X) * 0.1)
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]

        if idx_arr is not None:
            idx_train = idx_arr[:train_size]
            idx_val = idx_arr[train_size:train_size+val_size]
            idx_test = idx_arr[train_size+val_size:]
            return (X_train, y_train, idx_train), (X_val, y_val, idx_val), (X_test, y_test, idx_test)
        else:
            return (X_train, y_train), (X_val, y_val), (X_test, y_test)

class GRUModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(GRUModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # 简化模型：使用一个标准的双向GRU，移除注意力机制
        # Dropout只在有多层时才在层间应用，这是标准做法
        self.gru = nn.GRU(input_size, self.hidden_size, self.num_layers,
                         batch_first=True,
                         bidirectional=True,
                         dropout=dropout if num_layers > 1 else 0)

        # 新增：层归一化，稳定训练
        self.layer_norm = nn.LayerNorm(self.hidden_size * 2)

        # 新增：注意力层（将时间维度的每个时刻投影为1维权重）
        self.attention = nn.Linear(self.hidden_size * 2, 1)

        # 全连接层现在直接接收来自GRU的输出
        # 因为是双向的，所以输入维度是 hidden_size * 2
        self.fc = nn.Sequential(
            nn.Linear(self.hidden_size * 2, self.hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(self.hidden_size, output_size)
        )

    def forward(self, x):
        # 初始化隐藏状态
        h0 = torch.zeros(self.num_layers * 2, x.size(0), self.hidden_size).to(x.device)

        # GRU前向传播
        gru_out, _ = self.gru(x, h0)

        # 应用层归一化
        gru_out = self.layer_norm(gru_out)

        # 新增：基于注意力的上下文向量，而非简单取最后一步
        attn_scores = self.attention(gru_out)           # (batch, seq_len, 1)
        attn_weights = torch.softmax(attn_scores, dim=1) # (batch, seq_len, 1)
        context = torch.sum(attn_weights * gru_out, dim=1)  # (batch, hidden*2)

        # 通过全连接层得到最终输出
        out = self.fc(context)
        return out

class CompatibleAttention(nn.Module):
    """兼容老GPU架构的注意力机制"""

    def __init__(self, embed_dim, num_heads, dropout=0.1):
        super(CompatibleAttention, self).__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads

        assert self.head_dim * num_heads == embed_dim, "embed_dim must be divisible by num_heads"

        self.q_linear = nn.Linear(embed_dim, embed_dim)
        self.k_linear = nn.Linear(embed_dim, embed_dim)
        self.v_linear = nn.Linear(embed_dim, embed_dim)
        self.out_linear = nn.Linear(embed_dim, embed_dim)

        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5

    def forward(self, query, key, value):
        batch_size, seq_len, _ = query.shape

        # 线性变换
        Q = self.q_linear(query)
        K = self.k_linear(key)
        V = self.v_linear(value)

        # 重塑为多头格式
        Q = Q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale
        attn_weights = torch.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # 应用注意力权重
        attn_output = torch.matmul(attn_weights, V)

        # 重塑回原始格式
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.embed_dim
        )

        # 输出投影
        output = self.out_linear(attn_output)

        return output, attn_weights

class TransformerGRUModel(nn.Module):
    """兼容版Transformer-GRU混合架构"""

    def __init__(self, input_size, hidden_size, num_layers, output_size,
                 dropout=0.2, num_heads=8, transformer_layers=2):
        super(TransformerGRUModel, self).__init__()

        # 确保hidden_size能被num_heads整除
        if hidden_size % num_heads != 0:
            adjusted_hidden_size = (hidden_size // num_heads) * num_heads
            if adjusted_hidden_size < num_heads:
                adjusted_hidden_size = num_heads
            print(f"⚠️  调整hidden_size: {hidden_size} → {adjusted_hidden_size} (确保能被{num_heads}整除)")
            hidden_size = adjusted_hidden_size

        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # 输入投影层
        self.input_projection = nn.Linear(input_size, hidden_size)

        # 根据GPU能力选择Transformer实现
        if SM_VERSION >= 80:
            # 新架构使用原生Transformer
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=hidden_size,
                nhead=num_heads,
                dim_feedforward=hidden_size * 4,
                dropout=dropout,
                batch_first=True
            )
            self.transformer_encoder = nn.TransformerEncoder(
                encoder_layer,
                num_layers=transformer_layers
            )
            self.use_native_transformer = True
        else:
            # 老架构使用简化实现
            self.transformer_layers = nn.ModuleList([
                nn.Sequential(
                    CompatibleAttention(hidden_size, num_heads, dropout),
                    nn.LayerNorm(hidden_size),
                    nn.Linear(hidden_size, hidden_size * 4),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(hidden_size * 4, hidden_size),
                    nn.LayerNorm(hidden_size)
                ) for _ in range(transformer_layers)
            ])
            self.use_native_transformer = False

        # GRU层（处理时序模式）
        self.gru = nn.GRU(
            hidden_size, hidden_size, num_layers,
            batch_first=True, bidirectional=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # 层归一化
        self.layer_norm1 = nn.LayerNorm(hidden_size)
        self.layer_norm2 = nn.LayerNorm(hidden_size * 2)

        # 兼容的注意力机制
        self.attention = CompatibleAttention(
            embed_dim=hidden_size * 2,
            num_heads=num_heads,
            dropout=dropout
        )

        # 输出层
        self.fc = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, output_size)
        )

    def forward(self, x):
        batch_size, _, _ = x.shape

        # 输入投影
        x_proj = self.input_projection(x)
        x_proj = self.layer_norm1(x_proj)

        # Transformer编码器处理
        if self.use_native_transformer:
            transformer_out = self.transformer_encoder(x_proj)
        else:
            transformer_out = x_proj
            for layer in self.transformer_layers:
                # 简化的Transformer层
                attn_out, _ = layer[0](transformer_out, transformer_out, transformer_out)
                transformer_out = layer[1](transformer_out + attn_out)

                ffn_out = layer[2:6](transformer_out)
                transformer_out = layer[6](transformer_out + ffn_out)

        # GRU处理
        h0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        gru_out, _ = self.gru(transformer_out, h0)
        gru_out = self.layer_norm2(gru_out)

        # 兼容的注意力
        attn_out, _ = self.attention(gru_out, gru_out, gru_out)

        # 全局平均池化 + 最后时刻输出的组合
        global_avg = torch.mean(attn_out, dim=1)
        last_output = attn_out[:, -1, :]
        combined = (global_avg + last_output) / 2

        # 输出预测
        output = self.fc(combined)
        return output

class LearnedIMFEnsemble(nn.Module):
    """学习权重的IMF集成网络"""

    def __init__(self, num_imfs, context_size=64):
        super(LearnedIMFEnsemble, self).__init__()
        self.num_imfs = num_imfs

        # 上下文特征提取网络
        self.context_network = nn.Sequential(
            nn.Linear(num_imfs, context_size),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(context_size, context_size // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        # 权重生成网络
        self.weight_network = nn.Sequential(
            nn.Linear(context_size // 2, num_imfs),
            nn.Softmax(dim=1)
        )

        # 残差连接的权重
        self.residual_weight = nn.Parameter(torch.tensor(0.1))

    def forward(self, imf_predictions):
        """
        Args:
            imf_predictions: (batch_size, num_imfs) 各IMF分量的预测值
        Returns:
            ensemble_output: (batch_size, 1) 集成后的预测值
        """
        # 提取上下文特征
        context = self.context_network(imf_predictions)

        # 生成自适应权重
        weights = self.weight_network(context)

        # 加权求和
        weighted_sum = torch.sum(weights * imf_predictions, dim=1, keepdim=True)

        # 添加简单平均作为残差连接
        simple_avg = torch.mean(imf_predictions, dim=1, keepdim=True)
        ensemble_output = weighted_sum + self.residual_weight * simple_avg

        return ensemble_output

class WeightedMSELoss(nn.Module):
    """对大负荷样本赋予更高损失权重的 MSE。

    权重 = 1 + alpha * (y_true / y_max)。当 alpha > 0 时，
    负荷越高，权重越大，模型会更加关注峰值时段。
    """

    def __init__(self, alpha: float = 1.0):
        super().__init__()
        self.alpha = alpha

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 在当前 batch 内计算 max，避免全局扫描
        y_max = torch.max(target.detach()) + 1e-6
        weights = 1.0 + self.alpha * (target / y_max)
        return torch.mean(weights * (pred - target) ** 2)

class ZeroAwareLoss(nn.Module):
    """零值感知损失函数，解决数据不平衡问题。

    对非零值给予更高权重，同时结合MSE和分位数损失。
    """

    def __init__(self, zero_weight: float = 0.3, nonzero_weight: float = 2.0,
                 quantile_alpha: float = 0.1):
        super().__init__()
        self.zero_weight = zero_weight
        self.nonzero_weight = nonzero_weight
        self.quantile_alpha = quantile_alpha
        self.mse_loss = nn.MSELoss(reduction='none')

    def quantile_loss(self, pred: torch.Tensor, target: torch.Tensor,
                     quantiles: list = [0.1, 0.5, 0.9]) -> torch.Tensor:
        """分位数损失，改善极值预测"""
        losses = []
        for q in quantiles:
            error = target - pred
            loss = torch.where(error >= 0, q * error, (q - 1) * error)
            losses.append(loss.mean())
        return torch.stack(losses).mean()

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 零值感知权重
        is_nonzero = (target > 1e-6).float()
        weights = is_nonzero * self.nonzero_weight + (1 - is_nonzero) * self.zero_weight

        # 加权MSE损失
        mse_losses = self.mse_loss(pred, target)
        weighted_mse = (weights * mse_losses).mean()

        # 分位数损失（仅对非零值）
        if torch.sum(is_nonzero) > 0:
            nonzero_mask = is_nonzero.bool()
            quantile_loss = self.quantile_loss(pred[nonzero_mask], target[nonzero_mask])
        else:
            quantile_loss = torch.tensor(0.0, device=pred.device)

        return weighted_mse + self.quantile_alpha * quantile_loss

class AdaptiveWeightedLoss(nn.Module):
    """自适应加权损失函数，结合时间段和负荷大小的权重。"""

    def __init__(self, peak_hours: list = [18, 19, 20, 21, 22],
                 peak_weight: float = 1.5, base_weight: float = 1.0):
        super().__init__()
        self.peak_hours = peak_hours
        self.peak_weight = peak_weight
        self.base_weight = base_weight
        self.mse_loss = nn.MSELoss(reduction='none')

    def get_time_weights(self, batch_size: int, device: torch.device) -> torch.Tensor:
        """获取时间段权重（这里简化处理，实际应用中需要从输入中提取时间信息）"""
        # 简化版本：随机生成时间权重，实际应用中应该从特征中提取
        # 这里假设峰值时段的概率为30%
        is_peak = torch.rand(batch_size, device=device) < 0.3
        return torch.where(is_peak, self.peak_weight, self.base_weight)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 负荷大小权重
        target_max = torch.max(target) + 1e-6
        load_weights = 1.0 + (target / target_max)

        # 时间段权重
        time_weights = self.get_time_weights(target.size(0), target.device)

        # 零值感知权重
        zero_weights = torch.where(target > 1e-6, 2.0, 0.5)

        # 组合权重
        combined_weights = load_weights * time_weights * zero_weights

        # 加权MSE
        mse_losses = self.mse_loss(pred, target)
        return (combined_weights * mse_losses).mean()

class SSA:
    def __init__(self, pop_size=12, max_iter=10, n_workers=None, use_parallel=True, log_callback=None):
        self.pop_size = pop_size
        self.max_iter = max_iter
        self.use_parallel = use_parallel
        self.n_workers = n_workers or min(cpu_count(), 4)
        self.fitness_cache = {}  # 缓存已计算的适应度
        self.convergence_history = []
        self.best_solution = None
        self.best_fitness = float('-inf')
        self.log_callback = log_callback  # 添加日志回调支持

    def _log(self, message):
        """统一的日志输出方法"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    def optimize(self, train_loader, val_loader, input_size, output_size):
        """优化方法，支持并行和串行两种模式"""

        if self.use_parallel:
            return self._parallel_optimize(train_loader, val_loader, input_size, output_size)
        else:
            return self._serial_optimize(train_loader, val_loader, input_size, output_size)

    def _parallel_optimize(self, train_loader, val_loader, input_size, output_size):
        """并行优化实现"""
        self._log(f"🚀 启动并行SSA优化 (进程数: {self.n_workers})")

        # 初始化种群
        population = self._initialize_population()

        # 准备训练数据
        train_data, val_data = self._prepare_data_for_parallel(train_loader, val_loader)

        best_solution = None
        best_fitness = float('-inf')

        # 使用并行评估器
        with ParallelEvaluator(n_workers=self.n_workers, use_threading=False) as evaluator:

            for iteration in range(self.max_iter):
                self._log(f"📊 迭代 {iteration + 1}/{self.max_iter}")

                # 并行评估整个种群
                start_time = time.time()
                fitness_values = evaluator.evaluate_population(
                    population, train_data, val_data, input_size, output_size
                )
                eval_time = time.time() - start_time

                # 更新最佳解
                for i, fitness in enumerate(fitness_values):
                    if fitness > best_fitness:
                        best_fitness = fitness
                        best_solution = population[i].copy()

                # 记录收敛历史
                self.convergence_history.append({
                    'iteration': iteration,
                    'best_fitness': best_fitness,
                    'avg_fitness': np.mean(fitness_values),
                    'eval_time': eval_time
                })

                # 显示进度
                _, perf_monitor, _ = get_global_managers()
                progress_info = perf_monitor.get_progress_info()
                if isinstance(progress_info, dict):
                    self._log(f"📊 迭代 {iteration + 1}/{self.max_iter}")
                    self._log(f"   最佳适应度: {best_fitness:.4f}")
                    self._log(f"   平均适应度: {np.mean(fitness_values):.4f}")
                    self._log(f"   评估时间: {eval_time:.2f}s")
                    self._log(f"   任务速度: {progress_info.get('tasks_per_second', 0):.2f} 任务/秒")

                # 早停检查
                if self._should_early_stop():
                    self._log(f"🛑 早停于迭代 {iteration + 1}")
                    break

                # 更新种群
                population = self._parallel_update_population(population, best_solution)

        self.best_solution = best_solution
        self.best_fitness = best_fitness

        self._log(f"✅ 并行优化完成，最佳适应度: {best_fitness:.4f}")
        return best_solution

    def _serial_optimize(self, train_loader, val_loader, input_size, output_size):
        """串行优化实现（原始方法）"""
        population = self._initialize_population()
        best_solution = None
        best_fitness = float('-inf')

        for iteration in range(self.max_iter):
            for i in range(self.pop_size):
                fitness = self._evaluate_fitness(
                    population[i], train_loader, val_loader, input_size, output_size
                )
                if fitness > best_fitness:  # 注意：这里改为最大化R²
                    best_fitness = fitness
                    best_solution = population[i].copy()

            for i in range(self.pop_size):
                if random.random() < 0.5:
                    population[i] = self._update_follower(population[i], best_solution)
                else:
                    population[i] = self._update_discoverer(population[i], best_solution)
            self._log(f'📊 迭代 {iteration + 1}/{self.max_iter}, 最佳适应度: {best_fitness:.4f}')

        return best_solution

    def _prepare_data_for_parallel(self, train_loader, val_loader):
        """为并行处理准备数据"""
        # 将DataLoader转换为numpy数组
        X_train_list, y_train_list = [], []
        X_val_list, y_val_list = [], []

        for X_batch, y_batch in train_loader:
            # 确保张量在CPU上再转换为numpy
            X_train_list.append(X_batch.cpu().numpy())
            y_train_list.append(y_batch.cpu().numpy())

        for X_batch, y_batch in val_loader:
            # 确保张量在CPU上再转换为numpy
            X_val_list.append(X_batch.cpu().numpy())
            y_val_list.append(y_batch.cpu().numpy())

        X_train = np.vstack(X_train_list)
        y_train = np.hstack(y_train_list)
        X_val = np.vstack(X_val_list)
        y_val = np.hstack(y_val_list)

        return (X_train, y_train), (X_val, y_val)

    def _should_early_stop(self, patience=3, min_improvement=1e-4):
        """检查是否应该早停"""
        if len(self.convergence_history) < patience + 1:
            return False

        recent_fitness = [h['best_fitness'] for h in self.convergence_history[-patience-1:]]

        # 检查最近几次迭代是否有显著改善
        for i in range(1, len(recent_fitness)):
            if recent_fitness[i] - recent_fitness[i-1] > min_improvement:
                return False

        return True

    def _parallel_update_population(self, population, best_solution):
        """并行更新种群"""
        updated_population = []

        # 使用线程池进行种群更新（这是轻量级操作）
        with ThreadPoolExecutor(max_workers=min(self.n_workers, len(population))) as executor:
            futures = []

            for individual in population:
                if random.random() < 0.5:
                    future = executor.submit(self._update_follower, individual, best_solution)
                else:
                    future = executor.submit(self._update_discoverer, individual, best_solution)
                futures.append(future)

            for future in as_completed(futures):
                updated_population.append(future.result())

        return updated_population

    def get_optimization_stats(self):
        """获取优化统计信息"""
        if not self.convergence_history:
            return None

        return {
            'total_iterations': len(self.convergence_history),
            'best_fitness': self.best_fitness,
            'convergence_history': self.convergence_history,
            'total_eval_time': sum(h['eval_time'] for h in self.convergence_history),
            'avg_eval_time': np.mean([h['eval_time'] for h in self.convergence_history]),
            'final_improvement': self.convergence_history[-1]['best_fitness'] - self.convergence_history[0]['best_fitness'] if len(self.convergence_history) > 1 else 0
        }

    def _initialize_population(self):
        population = []
        hidden_sizes = [64, 128, 256, 384, 512]
        num_layers_options = [1, 2, 3]
        batch_sizes = [16, 32, 64, 128, 256]
        for _ in range(self.pop_size):
            solution = {
                'hidden_size': int(random.choice(hidden_sizes)),
                'num_layers': int(random.choice(num_layers_options)),
                'dropout': round(random.uniform(0.1, 0.4), 2),
                'learning_rate': round(random.uniform(1e-4, 5e-3), 6),
                'batch_size': int(random.choice(batch_sizes)),
                'loss_type': random.choice(['mse', 'huber', 'mix', 'wmse', 'zero_aware', 'adaptive']),
                'weight_decay': round(10 ** random.uniform(-5, -2), 6),
                'alpha': round(random.uniform(0.5, 2.0), 2),  # 仅对 wmse 有效
                'model_type': random.choice(['gru', 'transformer_gru'])
            }
            population.append(solution)
        return population
    def _evaluate_fitness(self, solution, train_loader, val_loader, input_size, output_size):
        model = SSAVMDGRU(
            input_size=input_size,
            hidden_size=int(solution['hidden_size']),
            num_layers=int(solution['num_layers']),
            output_size=output_size,
            dropout=solution['dropout'],
            loss_type=solution['loss_type'],
            weight_alpha=solution.get('alpha', 1.0),
            model_type=solution.get('model_type', 'gru')
        )
        model.optimizer = torch.optim.AdamW(
            model.gru_model.parameters(),
            lr=solution['learning_rate'],
            weight_decay=solution['weight_decay']
        )
        
        # 使用临时文件进行模型评估，避免冲突
        temp_model_path = f"temp_ssa_eval_model_{os.getpid()}.pth"
        model.train(train_loader, val_loader, num_epochs=5, patience=2, model_save_path=temp_model_path)
        
        # 新增：同时考虑 R²，使得算法更关注整体拟合优度
        y_true, y_pred = [], []
        model.gru_model.eval()
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                X_batch = X_batch.to(model.device, non_blocking=True)
                outputs = model.gru_model(X_batch).squeeze().cpu().numpy()
                y_true.extend(y_batch.cpu().numpy())
                y_pred.extend(outputs)
        r2_val = r2_score(np.array(y_true), np.array(y_pred)) if len(y_true) > 0 else 0.0
        # 直接使用R²作为适应度（最大化）
        fitness = r2_val
        
        # 清理临时模型文件
        try:
            if os.path.exists(temp_model_path):
                os.remove(temp_model_path)
        except OSError as e:
            print(f"Error removing temporary model file {temp_model_path}: {e}")
            
        return fitness

    def _ensure_hidden_size_compatibility(self, hidden_size, num_heads=8):
        """确保hidden_size能被num_heads整除"""
        if hidden_size % num_heads != 0:
            # 调整到最近的较大可整除值
            adjusted = ((hidden_size // num_heads) + 1) * num_heads
            if adjusted < num_heads:
                adjusted = num_heads
            return adjusted
        return hidden_size

    def _update_discoverer(self, current, best):
        new_solution = current.copy()
        for key in current.keys():
            if random.random() < 0.5:
                if key == 'hidden_size':
                    new_value = int(best[key] + random.uniform(-0.1, 0.1) * (best[key] - current[key]))
                    new_value = max(64, new_value)  # 最小值64
                    # 确保能被8整除
                    new_solution[key] = self._ensure_hidden_size_compatibility(new_value)
                elif key in ['num_layers', 'batch_size']:
                    new_value = int(best[key] + random.uniform(-0.1, 0.1) * (best[key] - current[key]))
                    new_solution[key] = max(1, new_value)
                elif key == 'dropout':
                    new_value = best[key] + random.uniform(-0.1, 0.1) * (best[key] - current[key])
                    new_solution[key] = round(max(0.1, min(0.6, new_value)), 2)
                elif key == 'learning_rate':
                    new_value = best[key] + random.uniform(-0.1, 0.1) * (best[key] - current[key])
                    new_solution[key] = round(max(0.00005, min(0.01, new_value)), 6)
                elif key == 'weight_decay':
                    new_value = best[key] + random.uniform(-0.3, 0.3) * (best[key] - current[key])
                    new_solution[key] = round(max(1e-6, min(0.05, new_value)), 6)
                elif key == 'alpha':
                    new_value = best[key] + random.uniform(-0.2, 0.2) * (best[key] - current[key])
                    new_solution[key] = round(max(0.1, min(5.0, new_value)), 2)
                elif key == 'loss_type':
                    new_solution[key] = best[key] if random.random() < 0.5 else current[key]
        return new_solution
    def _update_follower(self, current, best):
        new_solution = current.copy()
        for key in current.keys():
            if random.random() < 0.5:
                if key == 'hidden_size':
                    new_value = int(current[key] + random.uniform(-0.1, 0.1) * (best[key] - current[key]))
                    new_value = max(64, new_value)  # 最小值64
                    # 确保能被8整除
                    new_solution[key] = self._ensure_hidden_size_compatibility(new_value)
                elif key in ['num_layers', 'batch_size']:
                    new_value = int(current[key] + random.uniform(-0.1, 0.1) * (best[key] - current[key]))
                    new_solution[key] = max(1, new_value)
                elif key == 'dropout':
                    new_value = current[key] + random.uniform(-0.1, 0.1) * (best[key] - current[key])
                    new_solution[key] = round(max(0.1, min(0.6, new_value)), 2)
                elif key == 'learning_rate':
                    new_value = current[key] + random.uniform(-0.1, 0.1) * (best[key] - current[key])
                    new_solution[key] = round(max(0.00005, min(0.01, new_value)), 6)
                elif key == 'weight_decay':
                    new_value = current[key] + random.uniform(-0.3, 0.3) * (best[key] - current[key])
                    new_solution[key] = round(max(1e-6, min(0.05, new_value)), 6)
                elif key == 'alpha':
                    new_value = current[key] + random.uniform(-0.2, 0.2) * (best[key] - current[key])
                    new_solution[key] = round(max(0.1, min(5.0, new_value)), 2)
                elif key == 'loss_type':
                    new_solution[key] = best[key] if random.random() < 0.5 else current[key]
        return new_solution

class ChargingDataset(Dataset):
    def __init__(self, X, y, device: torch.device = DEVICE):
        """将数据直接放到目标 device 上，减少训练时的 CPU→GPU 拷贝。

        Args:
            X (np.ndarray): 输入序列，形状 (N, seq_len, feature_dim)
            y (np.ndarray): 目标值，形状 (N,)
            device (torch.device, optional): 目标设备，默认为全局 DEVICE
        """
        self.X = torch.as_tensor(X, dtype=torch.float32, device=device)
        self.y = torch.as_tensor(y, dtype=torch.float32, device=device)
        
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]
        
    @staticmethod
    def collate_fn(batch):
        X_batch = torch.stack([item[0] for item in batch])
        y_batch = torch.stack([item[1] for item in batch])
        return X_batch, y_batch

class SSAVMDGRU:
    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2,
                 loss_type='mse', weight_alpha: float = 1.0, model_type='gru'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")

        # 根据模型类型选择架构
        if model_type == 'transformer_gru':
            self.gru_model = TransformerGRUModel(
                input_size, hidden_size, num_layers, output_size, dropout=dropout
            ).to(self.device)
        else:
            self.gru_model = GRUModel(
                input_size, hidden_size, num_layers, output_size, dropout=dropout
            ).to(self.device)
        if loss_type == 'huber':
            self.criterion = nn.HuberLoss()
        elif loss_type == 'mae':
            self.criterion = nn.L1Loss()
        elif loss_type == 'mix':
            # 组合损失：MSE 与 MAE 加权求和，可更好地兼顾偏差和方差
            self._mse_loss = nn.MSELoss()
            self._mae_loss = nn.L1Loss()
            def _mixed_loss(pred, target, alpha: float = 0.5):
                """混合损失函数（MSE * alpha + MAE * (1 - alpha)）"""
                return alpha * self._mse_loss(pred, target) + (1 - alpha) * self._mae_loss(pred, target)
            self.criterion = _mixed_loss
        elif loss_type == 'wmse':
            self.criterion = WeightedMSELoss(alpha=weight_alpha)
        elif loss_type == 'zero_aware':
            self.criterion = ZeroAwareLoss()
        elif loss_type == 'adaptive':
            self.criterion = AdaptiveWeightedLoss()
        else:
            self.criterion = nn.MSELoss()
        
        # 使用AdamW优化器并添加权重衰减
        self.optimizer = torch.optim.AdamW(
            self.gru_model.parameters(),
            lr=0.001,
            weight_decay=0.01
        )
        
        # 使用余弦退火学习率调度器
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=10,  # 第一次重启的周期
            T_mult=2,  # 每次重启后周期长度的倍数
            eta_min=1e-6  # 最小学习率
        )
        
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = True
            self.scaler = torch.amp.GradScaler('cuda')
    
    def evaluate(self, val_loader):
        self.gru_model.eval()
        val_loss = 0
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                X_batch = X_batch.to(self.device, non_blocking=True)
                y_batch = y_batch.to(self.device, non_blocking=True)
                
                if self.device.type == 'cuda':
                    with torch.amp.autocast('cuda'):
                        outputs = self.gru_model(X_batch)
                        loss = self.criterion(outputs.squeeze(), y_batch)
                else:
                    outputs = self.gru_model(X_batch)
                    loss = self.criterion(outputs.squeeze(), y_batch)

                if torch.isnan(loss) or torch.isinf(loss):
                    continue
                val_loss += loss.item()
        return val_loss / max(1, len(val_loader))
    
    def train(self, train_loader, val_loader, num_epochs=100, patience=10, model_save_path='best_model.pth',
              progress_callback=None, log_callback=None):
        best_val_loss = float('inf')
        patience_counter = 0

        # 初始化日志
        if log_callback:
            log_callback(f"开始模型训练...")
            log_callback(f"训练参数: epochs={num_epochs}, patience={patience}")

        for epoch in range(num_epochs):
            self.gru_model.train()
            train_loss = 0

            # 根据是否有回调决定是否使用tqdm
            if log_callback:
                # 使用回调模式，不显示tqdm
                train_iterator = train_loader
                if log_callback:
                    log_callback(f"Epoch {epoch+1}/{num_epochs} - 开始训练...")
            else:
                # 使用tqdm显示进度
                train_iterator = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
            
            for batch_idx, (X_batch, y_batch) in enumerate(train_iterator):
                X_batch = X_batch.to(self.device, non_blocking=True)
                y_batch = y_batch.to(self.device, non_blocking=True)
                
                self.optimizer.zero_grad()
                
                if self.device.type == 'cuda':
                    # 使用自动混合精度训练
                    with torch.amp.autocast('cuda'):
                        outputs = self.gru_model(X_batch)
                        loss = self.criterion(outputs.squeeze(), y_batch)
                    
                    if torch.isnan(loss) or torch.isinf(loss):
                        warning_msg = f"[警告] loss=NaN, batch={batch_idx}, X_batch统计: mean={X_batch.mean().item():.4f}, std={X_batch.std().item():.4f}"
                        if log_callback:
                            log_callback(warning_msg)
                        else:
                            print(warning_msg)
                        continue
                    
                    # 使用scaler进行反向传播
                    self.scaler.scale(loss).backward()
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.gru_model.parameters(), max_norm=1.0)
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:  # CPU execution
                    outputs = self.gru_model(X_batch)
                    loss = self.criterion(outputs.squeeze(), y_batch)
                    if torch.isnan(loss) or torch.isinf(loss):
                        warning_msg = f"[警告] loss=NaN, batch={batch_idx}, X_batch统计: mean={X_batch.mean().item():.4f}, std={X_batch.std().item():.4f}"
                        if log_callback:
                            log_callback(warning_msg)
                        else:
                            print(warning_msg)
                        continue
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.gru_model.parameters(), max_norm=1.0)
                    self.optimizer.step()
                
                train_loss += loss.item()

                # 更新进度显示
                if hasattr(train_iterator, 'set_postfix'):
                    train_iterator.set_postfix({'loss': f'{loss.item():.4f}'})
                elif log_callback and batch_idx % 50 == 0:  # 每50个batch报告一次
                    log_callback(f"  Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")
            
            # 更新学习率
            self.scheduler.step()
            
            self.gru_model.eval()
            val_loss = 0

            # 根据是否有回调决定是否使用tqdm
            if log_callback:
                val_iterator = val_loader
                log_callback(f"Epoch {epoch+1}/{num_epochs} - 开始验证...")
            else:
                val_iterator = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            
            with torch.no_grad():
                for X_batch, y_batch in val_iterator:
                    X_batch = X_batch.to(self.device, non_blocking=True)
                    y_batch = y_batch.to(self.device, non_blocking=True)
                    
                    if self.device.type == 'cuda':
                        with torch.amp.autocast('cuda'):
                            outputs = self.gru_model(X_batch)
                            loss = self.criterion(outputs.squeeze(), y_batch)
                    else:
                        outputs = self.gru_model(X_batch)
                        loss = self.criterion(outputs.squeeze(), y_batch)

                    if torch.isnan(loss) or torch.isinf(loss):
                        continue
                    
                    val_loss += loss.item()

                    # 更新验证进度显示
                    if hasattr(val_iterator, 'set_postfix'):
                        val_iterator.set_postfix({'loss': f'{loss.item():.4f}'})
            
            avg_train_loss = train_loss / max(1, len(train_loader))
            avg_val_loss = val_loss / max(1, len(val_loader))

            # 输出epoch结果
            epoch_msg = f'Epoch [{epoch+1}/{num_epochs}], Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}, LR: {self.optimizer.param_groups[0]["lr"]:.6f}'
            if log_callback:
                log_callback(epoch_msg)
            else:
                print(f'\n{epoch_msg}')

            # 更新进度回调
            if progress_callback:
                # 传递详细的训练数据给回调函数
                progress_callback(epoch + 1, num_epochs, avg_train_loss, avg_val_loss)
            
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                torch.save(self.gru_model.state_dict(), model_save_path)
                patience_counter = 0
                save_msg = f"新的最佳模型已保存，验证损失: {best_val_loss:.6f}"
                if log_callback:
                    log_callback(save_msg)
                else:
                    print(save_msg)
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    early_stop_msg = f'Early stopping at epoch {epoch+1}'
                    if log_callback:
                        log_callback(early_stop_msg)
                    else:
                        print(early_stop_msg)
                    break
            
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

        # 训练完成日志
        if log_callback:
            log_callback(f"训练完成！最佳验证损失: {best_val_loss:.6f}")

        # 返回训练结果
        return {
            'best_val_loss': best_val_loss,
            'total_epochs': epoch + 1,
            'early_stopped': patience_counter >= patience
        }
    
    def predict(self, X, smooth_window=1):
        self.gru_model.eval()
        with torch.no_grad():
            X = torch.FloatTensor(X).to(self.device, non_blocking=True)
            if self.device.type == 'cuda':
                with torch.amp.autocast('cuda'):
                    predictions = self.gru_model(X).cpu().numpy().flatten()
            else:
                predictions = self.gru_model(X).cpu().numpy().flatten()
            if smooth_window > 1:
                predictions = pd.Series(predictions).rolling(window=smooth_window, min_periods=1, center=True).mean().values
            return predictions

def calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> dict:
    """
    计算并返回多个评估指标。
    会将预测值裁剪为非负，以符合充电负荷的物理约束。

    Args:
        y_true (np.ndarray): 真实值.
        y_pred (np.ndarray): 预测值.

    Returns:
        dict: 包含 MSE, MAE, RMSE, MAPE, R², sMAPE, CV(RMSE) 的字典.
    """
    # 清理非法值并确保输入是numpy array
    y_true = safe_array(y_true).flatten()
    y_pred = safe_array(y_pred).flatten()
    
    # 充电负荷应为非负, 若出现负数将其裁剪为 0
    y_pred = np.maximum(y_pred, 0)

    # --- 核心指标 ---
    mse = np.mean((y_true - y_pred)**2)
    mae = np.mean(np.abs(y_true - y_pred))
    rmse = np.sqrt(mse)

    # --- 百分比和比率指标 ---
    # MAPE: 避免 y_true 中有零导致计算错误
    zero_indices = np.where(y_true == 0)
    y_true_no_zero = np.delete(y_true, zero_indices)
    y_pred_no_zero = np.delete(y_pred, zero_indices)
    
    if len(y_true_no_zero) == 0:
        mape = 0.0
    else:
        mape = np.mean(np.abs((y_true_no_zero - y_pred_no_zero) / y_true_no_zero)) * 100
    
    # R²: 决定系数
    if len(y_true) > 1:
        r2 = r2_score(y_true, y_pred)
    else:
        r2 = 0.0

    # sMAPE: 对称平均绝对百分比误差, 鲁棒性更强
    denominator = (np.abs(y_true) + np.abs(y_pred)) / 2
    smape_val = np.abs(y_pred - y_true) / denominator
    smape_val[denominator == 0] = 0  # 处理真实值和预测值同时为0的情况
    smape = np.mean(smape_val) * 100

    # CV(RMSE): 均方根误差的变化系数, 用于比较不同尺度下的模型性能
    mean_y_true = np.mean(y_true)
    if mean_y_true != 0:
        cv_rmse = rmse / mean_y_true
    else:
        cv_rmse = np.inf  # 如果真实值均值为0, 则无法计算

    return {
        'MSE': mse,
        'MAE': mae, 
        'RMSE': rmse, 
        'MAPE': mape, 
        'R2': r2, 
        'sMAPE': smape, 
        'CV(RMSE)': cv_rmse
    }


def save_results(predictions, y_test, metrics, save_dir, load_scaler=None, results_manager=None):
    # The directory is now created in main() or by results_manager

    # 设置全局字体为微软雅黑
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 设置默认字体为微软雅黑
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.rcParams['font.family'] = 'sans-serif'  # 设置字体族
    
    # 反归一化预测值和实际值
    if load_scaler is not None:
        predictions = load_scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
        y_test = load_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
    
    # R2值已在main函数中计算并传入metrics字典，此处无需重复计算
    
    # 保存预测结果
    results_df = pd.DataFrame({
        'Actual': y_test,
        'Predicted': predictions.flatten()
    })
    
    results_df.to_csv(os.path.join(save_dir, 'predictions.csv'), index=False, encoding='utf-8-sig')
    metrics_df = pd.DataFrame([metrics])
    metrics_df.to_csv(os.path.join(save_dir, 'metrics.csv'), index=False, encoding='utf-8-sig')
    
    # 绘制历史预测结果
    plt.figure(figsize=(12, 6))
    plt.plot(y_test, label='实际值', color='blue')
    plt.plot(predictions, label='预测值', color='red')
    plt.title('充电负荷预测结果', fontsize=14, fontproperties='Microsoft YaHei')
    plt.xlabel('时间', fontsize=12, fontproperties='Microsoft YaHei')
    plt.ylabel('充电负荷', fontsize=12, fontproperties='Microsoft YaHei')
    plt.legend(prop={'family': 'Microsoft YaHei', 'size': 10})
    plt.grid(True)
    # 使用结果管理器保存图表（如果提供）
    if results_manager:
        results_manager.save_figure(plt.gcf(), 'prediction_plot')
    else:
        plt.savefig(os.path.join(save_dir, 'prediction_plot.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 绘制散点图
    plt.figure(figsize=(8, 8))
    plt.scatter(y_test, predictions, alpha=0.5)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
    plt.title('预测值与实际值对比', fontsize=14, fontproperties='Microsoft YaHei')
    plt.xlabel('实际值', fontsize=12, fontproperties='Microsoft YaHei')
    plt.ylabel('预测值', fontsize=12, fontproperties='Microsoft YaHei')
    plt.grid(True)
    # 使用结果管理器保存图表（如果提供）
    if results_manager:
        results_manager.save_figure(plt.gcf(), 'scatter_plot')
    else:
        plt.savefig(os.path.join(save_dir, 'scatter_plot.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 绘制误差分布
    errors = predictions.flatten() - y_test
    plt.figure(figsize=(10, 6))
    sns.histplot(errors, kde=True)
    plt.title('预测误差分布', fontsize=14, fontproperties='Microsoft YaHei')
    plt.xlabel('预测误差', fontsize=12, fontproperties='Microsoft YaHei')
    plt.ylabel('频数', fontsize=12, fontproperties='Microsoft YaHei')
    plt.grid(True)
    # 使用结果管理器保存图表（如果提供）
    if results_manager:
        results_manager.save_figure(plt.gcf(), 'error_distribution')
    else:
        plt.savefig(os.path.join(save_dir, 'error_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 创建评估指标的 Markdown 表格
    metrics_table = "| 指标 (Metric) | 全称 (Full Name) | 值 (Value) |\n"
    metrics_table += "|---|---|---|\n"
    metrics_table += f"| MSE | Mean Squared Error | {metrics.get('MSE', 0):.4f} |\n"
    metrics_table += f"| RMSE | Root Mean Squared Error | {metrics.get('RMSE', 0):.4f} |\n"
    metrics_table += f"| MAE | Mean Absolute Error | {metrics.get('MAE', 0):.4f} |\n"
    metrics_table += f"| R2 | R-squared | {metrics.get('R2', 0):.4f} |\n"
    
    summary = f"""
预测结果汇总 (生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')})

## 评估指标

{metrics_table}

## 生成文件

1. `predictions.csv` - 预测结果数据
2. `metrics.csv` - 评估指标数据
3. `prediction_plot.png` - 预测结果时间序列图
4. `scatter_plot.png` - 预测值与实际值散点图
5. `error_distribution.png` - 预测误差分布图
"""
    # 动态检查总功率图是否存在，若存在则添加到汇总信息
    run_timestamp = os.path.basename(save_dir)
    total_load_filename = f'total_load_profile_{run_timestamp}.png'
    total_load_plot_path = os.path.join(save_dir, total_load_filename)
    if os.path.exists(total_load_plot_path):
        summary += f"\n6. `{total_load_filename}` - 输入数据总有功功率图"

    with open(os.path.join(save_dir, 'summary.txt'), 'w', encoding='utf-8') as f:
        f.write(summary)

def plot_total_load(df: pd.DataFrame, save_dir: str):
    """
    绘制并保存总有功功率的整体数据图。

    Args:
        df (pd.DataFrame): 包含 'timestamp' 和 'charging_load' 列的数据框。
        save_dir (str): 保存图像的目录。
    """
    try:
        # 设置全局字体为微软雅黑
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.family'] = 'sans-serif'

        plt.figure(figsize=(15, 7))
        plt.plot(df['timestamp'], df['charging_load'], label='总有功功率 (kW)')
        plt.title('输入数据：总有功功率时序图', fontsize=16)
        plt.xlabel('时间', fontsize=12)
        plt.ylabel('功率 (kW)', fontsize=12)
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        
        run_timestamp = os.path.basename(save_dir)
        file_name = f'total_load_profile_{run_timestamp}.png'
        plot_path = os.path.join(save_dir, file_name)
        plt.savefig(plot_path, dpi=300)
        plt.close()
        
        print(f"总有功功率图已保存至: {plot_path}")

    except Exception as e:
        print(f"绘制总功率图时出错: {e}")

def find_optimal_sequence_length(df_featured, sequence_lengths=[24, 48, 72, 168], test_size=0.2):
    """
    测试不同序列长度的效果，找到最优配置。

    Args:
        df_featured: 已经进行特征工程的DataFrame
        sequence_lengths: 要测试的序列长度列表
        test_size: 测试集比例

    Returns:
        dict: 包含每个序列长度的性能指标
    """
    print("开始测试不同序列长度的效果...")
    results = {}

    for seq_len in sequence_lengths:
        print(f"测试序列长度: {seq_len}")
        try:
            # 创建临时预处理器
            temp_preprocessor = DataPreprocessor()

            # 准备序列数据
            X, y, _ = temp_preprocessor.prepare_sequences(df_featured.copy(), sequence_length=seq_len)

            if len(X) < 100:  # 数据太少，跳过
                print(f"序列长度 {seq_len} 产生的样本数太少 ({len(X)})，跳过")
                continue

            # 简单分割数据
            test_start = int(len(X) * (1 - test_size))
            X_train, X_test = X[:test_start], X[test_start:]
            y_train, y_test = y[:test_start], y[test_start:]

            # 创建简单的GRU模型进行快速测试
            model = GRUModel(
                input_size=X.shape[2],
                hidden_size=128,
                num_layers=2,
                output_size=1,
                dropout=0.2
            ).to(DEVICE)

            criterion = ZeroAwareLoss()
            optimizer = torch.optim.AdamW(model.parameters(), lr=0.001)

            # 快速训练（只训练几个epoch）
            model.train()
            for _ in range(5):
                optimizer.zero_grad()
                X_batch = torch.FloatTensor(X_train).to(DEVICE)
                y_batch = torch.FloatTensor(y_train).to(DEVICE)

                outputs = model(X_batch)
                loss = criterion(outputs.squeeze(), y_batch)
                loss.backward()
                optimizer.step()

            # 评估
            model.eval()
            with torch.no_grad():
                X_test_tensor = torch.FloatTensor(X_test).to(DEVICE)
                y_pred = model(X_test_tensor).cpu().numpy().flatten()

                # 反归一化
                y_pred_unscaled = temp_preprocessor.load_scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()
                y_test_unscaled = temp_preprocessor.load_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()

                # 计算指标
                metrics = calculate_metrics(y_test_unscaled, y_pred_unscaled)
                results[seq_len] = {
                    'R2': metrics['R2'],
                    'RMSE': metrics['RMSE'],
                    'MAE': metrics['MAE'],
                    'sample_count': len(X)
                }

                print(f"序列长度 {seq_len}: R² = {metrics['R2']:.4f}, RMSE = {metrics['RMSE']:.4f}")

        except Exception as e:
            print(f"测试序列长度 {seq_len} 时出错: {e}")
            continue

    # 找到最优序列长度
    if results:
        best_seq_len = max(results.keys(), key=lambda k: results[k]['R2'])
        print(f"\n最优序列长度: {best_seq_len} (R² = {results[best_seq_len]['R2']:.4f})")
        return best_seq_len, results
    else:
        print("所有序列长度测试都失败，使用默认值48")
        return 48, {}

def find_optimal_vmd_k(signal, alpha=2000, tau=0., DC=False, init=1, tol=1e-7, max_k=10):
    """
    通过中心频率法自动寻找VMD的最佳模态数K。
    该方法迭代不同的K值，并分析每个K值下各模态中心频率的间距。
    当增加K导致两个模态的中心频率过于接近时，表明发生了模态混叠，
    此时的K值被认为是过量的。最佳K值通常是在此现象发生之前的值。

    Args:
        signal (np.ndarray): 输入信号.
        alpha, tau, DC, init, tol: VMD算法的参数.
        max_k (int): 搜索的最大K值.

    Returns:
        int: 找到的最佳K值.
    """
    signal = safe_array(signal)
    min_dists = []
    print(f"开始通过中心频率法搜索最佳K值 (范围: 2-{max_k})...")
    
    for k in tqdm(range(2, max_k + 1), desc="搜索最佳K值"):
        try:
            _, _, omega = VMD(signal, alpha, tau, k, DC, init, tol)
            center_freqs = np.sort(omega[:, -1])
            
            if np.any(np.isnan(center_freqs)):
                print(f"VMD在K={k}时产生NaN频率，停止搜索。将使用上一个有效的K={k-1}。")
                return k - 1
            
            min_dist = np.min(np.diff(center_freqs))
            min_dists.append(min_dist)
        except Exception as e:
            print(f"VMD在K={k}时执行失败: {e}。将使用上一个有效的K={k-1}。")
            if k > 2:
                return k - 1
            else:
                return 2 # Fallback

    if len(min_dists) < 2:
        print("无法确定最佳K值，将使用默认值 K=5。")
        return 5

    # 启发式规则：寻找最小频率间距的"拐点"。
    # 我们计算间距的相对下降率，并找到下降最显著的点。
    # 最大的相对下降意味着从 K 增加到 K+1 时，某个模态被不必要地分成了两个。
    # 因此，最佳K值是在这个急剧下降发生之前的那个值。
    relative_drops = [((min_dists[i] - min_dists[i+1]) / min_dists[i]) if min_dists[i] > 0 else 0 for i in range(len(min_dists)-1)]
    
    if not relative_drops:
        print(f"无法找到明确的拐点，将使用搜索范围内的最大值 K={max_k}。")
        return max_k

    # `relative_drops` 的索引 `i` 对应于 K 从 `i+2` 变为 `i+3` 的情况。
    # `argmax` 将返回最大下降发生时的索引 `i`。
    # 因此，最佳的K值是 `i+2`。
    best_k_index = np.argmax(relative_drops)
    optimal_k = best_k_index + 2
    
    print(f"搜索完成。最小频率间距(K=2 to {max_k}): {[f'{d:.4f}' for d in min_dists]}")
    print(f"间距相对下降率: {[f'{d:.4f}' for d in relative_drops]}")
    print(f"找到最佳VMD模态数 K = {optimal_k}")
    
    return optimal_k

def vmd_decompose_series(series, K=5, alpha=2000, tau=0., DC=False, init=1, tol=1e-7):
    """
    使用VMD（Variational Mode Decomposition）将信号分解为K个IMF分量。
    """
    series = safe_array(series)
    # 调用vmdpy库进行分解。alpha是带宽参数，K是模态数量。
    imfs, _, _ = VMD(series, alpha, tau, K, DC, init, tol)
    imfs = safe_array(imfs)
    return imfs

def process_imf_component(args):
    """
    独立处理单个IMF分量的函数，用于多进程并行计算。
    包括：特征工程、模型训练、验证和预测。
    """
    idx, imf_data, df_featured, gpu_lock, results_dir, num_workers, pin_memory, sequence_length = args
    
    print(f"进程 {os.getpid()} 开始处理 IMF 分量 {idx+1}...")
    
    # 为每个IMF使用独立的预处理器，以隔离scaler
    imf_preprocessor = DataPreprocessor()
    
    # 使用带有预计算特征的DataFrame副本
    df_imf = df_featured.copy()
    # 仅将目标列'charging_load'替换为当前IMF分量
    df_imf['charging_load'] = imf_data
    
    # 重新基于当前IMF分量生成依赖历史的特征（滞后、滑动、EWMA等）
    df_imf = imf_preprocessor._create_time_features_dependent(df_imf)
    
    X, y, idx_list = imf_preprocessor.prepare_sequences(df_imf, sequence_length=sequence_length)
    (X_train, y_train, _), (X_val, y_val, _), (X_test, y_test, idx_test) = imf_preprocessor.split_data(X, y, idx_arr=idx_list)
    
    # 注意：在多进程中，每个进程创建自己的数据集实例
    train_dataset = ChargingDataset(X_train, y_train, device=DEVICE)
    val_dataset = ChargingDataset(X_val, y_val, device=DEVICE)

    # --- GPU 资源访问控制和并行SSA优化 ---
    with gpu_lock:
        print(f"进程 {os.getpid()} (IMF {idx+1}) 获得 GPU 锁，开始并行 SSA 优化...")

        # 获取GPU管理器实例（确保在子进程中正确初始化）
        gpu_mgr, _, _ = get_global_managers()

        # 分配GPU内存
        gpu_mgr.allocate_gpu_memory(os.getpid())

        # 创建并行SSA优化器
        ssa = SSA(
            pop_size=16,  # 减少种群大小以适应并行处理
            max_iter=10,  # 减少迭代次数，因为并行评估更高效
            n_workers=min(cpu_count()//2, 3),  # 每个IMF使用部分CPU核心
            use_parallel=True
        )

        best_params = ssa.optimize(
            train_loader=DataLoader(
                train_dataset, batch_size=32, shuffle=True, num_workers=num_workers,
                pin_memory=pin_memory, collate_fn=ChargingDataset.collate_fn,
                persistent_workers=True if num_workers > 0 else False
            ),
            val_loader=DataLoader(
                val_dataset, batch_size=32, num_workers=num_workers,
                pin_memory=pin_memory, collate_fn=ChargingDataset.collate_fn,
                persistent_workers=True if num_workers > 0 else False
            ),
            input_size=X.shape[2], output_size=1
        )

        # 获取优化统计信息
        stats = ssa.get_optimization_stats()
        if stats:
            print(f"进程 {os.getpid()} (IMF {idx+1}) SSA统计:")
            print(f"  总迭代数: {stats['total_iterations']}")
            print(f"  最佳适应度: {stats['best_fitness']:.4f}")
            print(f"  总评估时间: {stats['total_eval_time']:.2f}s")
            print(f"  平均评估时间: {stats['avg_eval_time']:.2f}s")

        # 释放GPU内存
        gpu_mgr.release_gpu_memory(os.getpid())

        print(f"进程 {os.getpid()} (IMF {idx+1}) 完成并行 SSA 优化。")

    print(f"IMF{idx+1}最优超参数: {best_params}")
    
    final_model = SSAVMDGRU(
        input_size=X.shape[2], hidden_size=best_params['hidden_size'],
        num_layers=best_params['num_layers'], output_size=1,
        dropout=best_params['dropout'], loss_type=best_params['loss_type'],
        weight_alpha=best_params.get('alpha', 1.0)
    )
    final_model.optimizer = torch.optim.AdamW(
        final_model.gru_model.parameters(), lr=best_params['learning_rate'],
        weight_decay=best_params['weight_decay']
    )

    train_loader = DataLoader(
        train_dataset, batch_size=best_params['batch_size'], shuffle=True,
        num_workers=num_workers, pin_memory=pin_memory,
        collate_fn=ChargingDataset.collate_fn,
        persistent_workers=True if num_workers > 0 else False
    )
    val_loader = DataLoader(
        val_dataset, batch_size=best_params['batch_size'],
        num_workers=num_workers, pin_memory=pin_memory,
        collate_fn=ChargingDataset.collate_fn,
        persistent_workers=True if num_workers > 0 else False
    )

    # --- GPU 资源访问控制 ---
    with gpu_lock:
        print(f"进程 {os.getpid()} (IMF {idx+1}) 获得 GPU 锁，开始最终模型训练...")
        model_save_path = os.path.join(results_dir, f'best_model_imf_{idx+1}.pth')
        final_model.train(train_loader, val_loader, num_epochs=200, patience=20, model_save_path=model_save_path)
        final_model.gru_model.load_state_dict(torch.load(model_save_path, map_location=DEVICE))
        print(f"进程 {os.getpid()} (IMF {idx+1}) 完成最终模型训练。")

    print(f"进程 {os.getpid()} (IMF {idx+1}) 开始 CPU 预测...")
    pred = final_model.predict(X_test, smooth_window=1)
    
    pred_unscaled = imf_preprocessor.load_scaler.inverse_transform(pred.reshape(-1, 1)).flatten()
    y_test_unscaled = imf_preprocessor.load_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
    
    pred_series = pd.Series(pred_unscaled, index=idx_test)
    true_series = pd.Series(y_test_unscaled, index=idx_test)

    print(f"进程 {os.getpid()} 完成 IMF 分量 {idx+1}。")
    return idx, pred_series, true_series

def main():
    torch.manual_seed(42)
    np.random.seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(42)
        torch.cuda.set_per_process_memory_fraction(0.8)
        torch.cuda.empty_cache()
        # 在Windows上使用多进程数据加载时，设置'spawn'是推荐做法，以避免CUDA初始化问题
        try:
            torch.multiprocessing.set_start_method('spawn', force=True)
            print("设置多处理启动方法为 'spawn' 以提高稳定性。")
        except RuntimeError:
            pass  # 如果已经设置，则忽略

    # 创建本次运行的唯一时间戳和结果目录
    run_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_dir = os.path.join('results', run_timestamp)
    os.makedirs(results_dir, exist_ok=True)

    # 自动根据GPU设置DataLoader参数
    # 此版本中 ChargingDataset 已直接返回位于 GPU 的张量，
    # DataLoader 再使用 pin_memory 会报 "cannot pin torch.cuda.FloatTensor" 错误。
    pin_memory = False
    
    # 设置DataLoader的num_workers参数
    num_workers = 0 if os.name == 'nt' else min(cpu_count(), 4)
    print(f"设置DataLoader num_workers为: {num_workers}")

    preprocessor = DataPreprocessor()
    print("加载和预处理数据...")
    df = preprocessor.load_and_clean_data('charging_data.csv')
    
    # 新增: 绘制总有功功率的整体数据图
    plot_total_load(df, results_dir)
    
    # 关键修正：首先基于原始信号生成所有特征
    print("从原始信号生成时间序列特征...")
    df_featured = preprocessor.create_time_features(df)

    # 新增：寻找最优序列长度
    print("寻找最优序列长度...")
    optimal_seq_len, _ = find_optimal_sequence_length(df_featured)
    print(f"选择的最优序列长度: {optimal_seq_len}")

    # 通过中心频率法自动寻找最佳VMD分量数K
    K = find_optimal_vmd_k(df['charging_load'].values, max_k=8) # 减少max_k以提高效率
    print(f"自动确定的最佳IMF分量个数K={K}")

    print("VMD分解原始负荷序列...")
    imfs = vmd_decompose_series(df['charging_load'].values, K=K)
    print(f"分解得到{K}个IMF分量")
    
    # --- 多进程并行处理 ---
    # 使用 Manager 创建一个进程安全的锁，用于保护对 GPU 的访问
    with Manager() as manager:
        gpu_lock = manager.Lock()
        
        # 创建一个参数列表，每个元素是 process_imf_component 函数所需的一组参数
        imf_tasks = [(idx, imfs[idx], df_featured, gpu_lock, results_dir, num_workers, pin_memory, optimal_seq_len) for idx in range(K)]
        
        # 设置并行进程数，不超过IMF分量数或CPU核心数
        max_workers = min(K, cpu_count())
        print(f"将使用 {max_workers} 个并行进程处理 {K} 个IMF分量...")

        all_pred_unscaled = [None] * K
        all_true_unscaled = [None] * K
        
        with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 使用 executor.map 来保持结果的顺序
            results_generator = executor.map(process_imf_component, imf_tasks)
            # 将生成器立即转换为列表以解决TypeError
            results_list = list(results_generator)
            
            for result_item in results_list:
                # 检查返回结果是否有效
                if result_item is None:
                    continue
                idx, pred_series, true_series = result_item
                all_pred_unscaled[idx] = pred_series
                all_true_unscaled[idx] = true_series
                print(f"主进程收到 IMF 分量 {idx+1} 的结果。")

    # 检查是否有任何一个任务失败
    if any(s is None for s in all_pred_unscaled):
        print("警告: 部分IMF分量处理失败，结果可能不完整。")
        # 过滤掉失败的任务
        all_pred_unscaled = [s for s in all_pred_unscaled if s is not None]
        all_true_unscaled = [s for s in all_true_unscaled if s is not None]

    # 对齐（外连接），缺失填0，然后逐行求和
    pred_df = pd.concat(all_pred_unscaled, axis=1).fillna(0)
    true_df = pd.concat(all_true_unscaled, axis=1).fillna(0)

    final_pred_series = pred_df.sum(axis=1)
    final_true_series = true_df.sum(axis=1)

    # 转换为numpy数组
    unclipped_pred = final_pred_series.values
    final_true = final_true_series.values

    # VMD分解和重构可能导致实际值（final_true）出现微小的负数
    # 这在物理上是不可能的，因此在计算指标和绘图前将其裁剪为0
    # 同时，这也确保了偏差计算的基准是物理上有效的值
    final_true = np.maximum(final_true, 0)
    
    # ------------------ 快速偏差校正 ------------------
    # 基于原始（未裁剪）的预测值和裁剪后的真实值来计算偏差
    bias = np.mean(final_true - unclipped_pred)
    print(f"\n应用均值偏差校正: bias = {bias:.4f} (kW)")
    corrected_pred = unclipped_pred + bias
    
    # 校正后，强制最终预测结果非负，以符合物理约束
    final_pred = np.maximum(corrected_pred, 0)

    # ------------------ 使用新的评估函数 ------------------
    metrics = calculate_metrics(final_true, final_pred)

    print("\n最终评估结果:")
    print(f"Test MSE: {metrics['MSE']:.4f}")
    print(f"Test RMSE: {metrics['RMSE']:.4f}")
    print(f"Test MAE: {metrics['MAE']:.4f}")
    print(f"Test R²: {metrics['R2']:.4f}")
    
    # 将 bias 持久化，便于推理阶段使用相同校正
    with open(os.path.join(results_dir, 'bias.txt'), 'w', encoding='utf-8') as f_bias:
        f_bias.write(f"{bias}\n")

    # 传入已经反归一化的数据，因此 load_scaler 设为 None
    save_results(final_pred, final_true, metrics, save_dir=results_dir, load_scaler=None)

if __name__ == '__main__':
    # 确保在 Windows 和 macOS 上使用 'spawn' 启动方法，这是多进程与CUDA协同工作的关键
    torch.multiprocessing.set_start_method('spawn', force=True)
    main() 