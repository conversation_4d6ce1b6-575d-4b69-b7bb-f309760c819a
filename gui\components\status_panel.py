import tkinter as tk
from tkinter import ttk
import psutil
import threading
import time
from datetime import datetime


class StatusPanel:
    """状态面板类"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)
        self.monitoring = False
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：系统状态
        left_frame = ttk.Frame(main_container)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 右侧：应用状态
        right_frame = ttk.Frame(main_container)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_system_status(left_frame)
        self.create_app_status(right_frame)
        
    def create_system_status(self, parent):
        """创建系统状态区域"""
        # 系统信息
        system_frame = ttk.LabelFrame(parent, text="💻 系统信息", padding=10)
        system_frame.pack(fill=tk.X, pady=(0, 10))
        
        # CPU信息
        cpu_frame = ttk.Frame(system_frame)
        cpu_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(cpu_frame, text="CPU使用率:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.cpu_var = tk.StringVar(value="0%")
        ttk.Label(cpu_frame, textvariable=self.cpu_var, font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))
        
        self.cpu_progress = ttk.Progressbar(cpu_frame, length=200, maximum=100)
        self.cpu_progress.pack(side=tk.RIGHT)
        
        # 内存信息
        memory_frame = ttk.Frame(system_frame)
        memory_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(memory_frame, text="内存使用率:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.memory_var = tk.StringVar(value="0%")
        ttk.Label(memory_frame, textvariable=self.memory_var, font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))
        
        self.memory_progress = ttk.Progressbar(memory_frame, length=200, maximum=100)
        self.memory_progress.pack(side=tk.RIGHT)
        
        # 磁盘信息
        disk_frame = ttk.Frame(system_frame)
        disk_frame.pack(fill=tk.X)
        
        ttk.Label(disk_frame, text="磁盘使用率:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.disk_var = tk.StringVar(value="0%")
        ttk.Label(disk_frame, textvariable=self.disk_var, font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))
        
        self.disk_progress = ttk.Progressbar(disk_frame, length=200, maximum=100)
        self.disk_progress.pack(side=tk.RIGHT)
        
        # GPU信息（如果可用）
        gpu_frame = ttk.LabelFrame(parent, text="🎮 GPU信息", padding=10)
        gpu_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.gpu_available_var = tk.StringVar(value="检测中...")
        ttk.Label(gpu_frame, textvariable=self.gpu_available_var, font=("Arial", 10)).pack()
        
        self.gpu_usage_frame = ttk.Frame(gpu_frame)
        self.gpu_usage_frame.pack(fill=tk.X, pady=(5, 0))
        
        # 网络信息
        network_frame = ttk.LabelFrame(parent, text="🌐 网络状态", padding=10)
        network_frame.pack(fill=tk.X)
        
        # 网络速度
        net_speed_frame = ttk.Frame(network_frame)
        net_speed_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(net_speed_frame, text="下载速度:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.download_speed_var = tk.StringVar(value="0 KB/s")
        ttk.Label(net_speed_frame, textvariable=self.download_speed_var, font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))
        
        ttk.Label(net_speed_frame, text="上传速度:", font=("Arial", 10, "bold")).pack(side=tk.RIGHT)
        self.upload_speed_var = tk.StringVar(value="0 KB/s")
        ttk.Label(net_speed_frame, textvariable=self.upload_speed_var, font=("Arial", 10)).pack(side=tk.RIGHT, padx=(10, 0))
        
    def create_app_status(self, parent):
        """创建应用状态区域"""
        # 应用信息
        app_frame = ttk.LabelFrame(parent, text="🚀 应用状态", padding=10)
        app_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 运行时间
        runtime_frame = ttk.Frame(app_frame)
        runtime_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(runtime_frame, text="运行时间:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.runtime_var = tk.StringVar(value="00:00:00")
        ttk.Label(runtime_frame, textvariable=self.runtime_var, font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))
        
        # 当前状态
        status_frame = ttk.Frame(app_frame)
        status_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(status_frame, text="当前状态:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.app_status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.app_status_var, font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))
        
        # 数据状态
        data_status_frame = ttk.Frame(app_frame)
        data_status_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(data_status_frame, text="数据状态:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.data_status_var = tk.StringVar(value="未加载")
        ttk.Label(data_status_frame, textvariable=self.data_status_var, font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))
        
        # 模型状态
        model_status_frame = ttk.Frame(app_frame)
        model_status_frame.pack(fill=tk.X)
        
        ttk.Label(model_status_frame, text="模型状态:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.model_status_var = tk.StringVar(value="未训练")
        ttk.Label(model_status_frame, textvariable=self.model_status_var, font=("Arial", 10)).pack(side=tk.LEFT, padx=(10, 0))
        
        # 日志信息
        log_frame = ttk.LabelFrame(parent, text="📝 系统日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建日志文本框
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=15, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志控制
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(log_controls, text="🗑️ 清空日志", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_controls, text="💾 保存日志", 
                  command=self.save_log).pack(side=tk.LEFT, padx=(0, 5))
        
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_controls, text="自动滚动", 
                       variable=self.auto_scroll_var).pack(side=tk.RIGHT)
        
        # 控制按钮
        control_frame = ttk.LabelFrame(parent, text="⚙️ 系统控制", padding=10)
        control_frame.pack(fill=tk.X)
        
        control_buttons = ttk.Frame(control_frame)
        control_buttons.pack(fill=tk.X)
        
        ttk.Button(control_buttons, text="🔄 刷新状态", 
                  command=self.refresh_status).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_buttons, text="📊 性能报告", 
                  command=self.generate_performance_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_buttons, text="🧹 清理缓存", 
                  command=self.clear_cache).pack(side=tk.LEFT)
        
        # 记录应用启动时间
        self.app_start_time = time.time()

        # 延迟添加初始日志，确保GUI完全初始化
        self.frame.after(500, lambda: self.add_log("应用启动完成"))
        
        # 启动实时更新
        self.frame.after(1000, self.start_real_time_updates)
        
    def start_monitoring(self):
        """开始系统监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_system, daemon=True)
        self.monitor_thread.start()
        
        # 启动运行时间更新
        self.update_runtime()
        
    def start_real_time_updates(self):
        """启动实时更新机制"""
        # 启动快速GPU监控
        self.update_gpu_info_real_time()
        
        # 启动应用状态实时检查
        self.check_app_status_real_time()
        
    def update_gpu_info_real_time(self):
        """实时更新GPU信息"""
        try:
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_info = []
                
                for i in range(gpu_count):
                    try:
                        gpu_name = torch.cuda.get_device_name(i)
                        memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)  # GB
                        memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)   # GB
                        memory_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)  # GB
                        
                        gpu_info.append(f"GPU {i}: {gpu_name}")
                        gpu_info.append(f"内存: {memory_allocated:.1f}GB / {memory_total:.1f}GB")
                        
                    except Exception as e:
                        gpu_info.append(f"GPU {i}: 信息获取失败")
                
                # 更新GUI显示
                if gpu_info:
                    self.gpu_available_var.set("\n".join(gpu_info[:2]))  # 只显示前2行避免界面过长
                else:
                    self.gpu_available_var.set("GPU 可用但获取信息失败")
                
            else:
                self.gpu_available_var.set("GPU 不可用")
                
        except ImportError:
            self.gpu_available_var.set("PyTorch 未安装")
        except Exception as e:
            self.gpu_available_var.set(f"GPU 检测错误: {str(e)}")
        finally:
            # 每3秒更新一次GPU信息
            if self.monitoring:
                self.frame.after(3000, self.update_gpu_info_real_time)
    
    def check_app_status_real_time(self):
        """实时检查应用状态"""
        if not self.monitoring:
            return
            
        def update_status():
            try:
                # 检查数据状态
                if hasattr(self.main_app, 'data_manager'):
                    if hasattr(self.main_app.data_manager, 'has_data') and self.main_app.data_manager.has_data():
                        self.data_status_var.set("已加载")
                    else:
                        self.data_status_var.set("未加载")
                
                # 检查模型状态和训练状态
                if hasattr(self.main_app, 'is_training') and self.main_app.is_training:
                    self.model_status_var.set("训练中")
                    self.app_status_var.set("训练中")
                elif hasattr(self.main_app, 'model_manager'):
                    if hasattr(self.main_app.model_manager, 'is_model_available') and self.main_app.model_manager.is_model_available():
                        self.model_status_var.set("已训练")
                        self.app_status_var.set("就绪")
                    else:
                        self.model_status_var.set("未训练")
                        if not hasattr(self.main_app, 'is_training') or not self.main_app.is_training:
                            self.app_status_var.set("就绪")
                
            except Exception as e:
                print(f"应用状态检查错误: {e}")
        
        try:
            # 确保在主线程中执行GUI更新
            if hasattr(self.frame, 'after'):
                self.frame.after(0, update_status)
                # 安排下次检查
                if self.monitoring:
                    self.frame.after(1000, self.check_app_status_real_time)
        except Exception as e:
            print(f"状态检查调度错误: {e}")
        
    def monitor_system(self):
        """监控系统状态"""
        last_net_io = psutil.net_io_counters()
        
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.frame.after(0, lambda: self.update_cpu(cpu_percent))
                
                # 内存使用率
                memory = psutil.virtual_memory()
                self.frame.after(0, lambda: self.update_memory(memory.percent))
                
                # 磁盘使用率
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                self.frame.after(0, lambda: self.update_disk(disk_percent))
                
                # 网络速度
                current_net_io = psutil.net_io_counters()
                download_speed = (current_net_io.bytes_recv - last_net_io.bytes_recv) / 1024  # KB/s
                upload_speed = (current_net_io.bytes_sent - last_net_io.bytes_sent) / 1024  # KB/s
                
                self.frame.after(0, lambda: self.update_network_speed(download_speed, upload_speed))
                last_net_io = current_net_io
                
                # GPU信息
                self.frame.after(0, self.update_gpu_info)
                
                # 应用状态
                self.frame.after(0, self.update_app_status)
                
            except Exception as e:
                error_msg = f"监控错误: {str(e)}"
                self.frame.after(0, lambda msg=error_msg: self.add_log(msg))
                
            time.sleep(2)  # 每2秒更新一次
            
    def update_cpu(self, cpu_percent):
        """更新CPU使用率"""
        self.cpu_var.set(f"{cpu_percent:.1f}%")
        self.cpu_progress['value'] = cpu_percent
        
    def update_memory(self, memory_percent):
        """更新内存使用率"""
        self.memory_var.set(f"{memory_percent:.1f}%")
        self.memory_progress['value'] = memory_percent
        
    def update_disk(self, disk_percent):
        """更新磁盘使用率"""
        self.disk_var.set(f"{disk_percent:.1f}%")
        self.disk_progress['value'] = disk_percent
        
    def update_network_speed(self, download_speed, upload_speed):
        """更新网络速度"""
        if download_speed > 1024:
            download_str = f"{download_speed/1024:.1f} MB/s"
        else:
            download_str = f"{download_speed:.1f} KB/s"
            
        if upload_speed > 1024:
            upload_str = f"{upload_speed/1024:.1f} MB/s"
        else:
            upload_str = f"{upload_speed:.1f} KB/s"
            
        self.download_speed_var.set(download_str)
        self.upload_speed_var.set(upload_str)
        
    def update_gpu_info(self):
        """更新GPU信息"""
        try:
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                current_device = torch.cuda.current_device()
                gpu_name = torch.cuda.get_device_name(current_device)
                
                # GPU内存使用情况
                memory_allocated = torch.cuda.memory_allocated(current_device) / 1024**3  # GB
                memory_cached = torch.cuda.memory_reserved(current_device) / 1024**3  # GB
                
                gpu_info = f"GPU可用: {gpu_name} ({gpu_count}个设备)\n"
                gpu_info += f"内存使用: {memory_allocated:.2f}GB / {memory_cached:.2f}GB"
                
                self.gpu_available_var.set(gpu_info)
            else:
                self.gpu_available_var.set("GPU不可用或未安装CUDA")
        except ImportError:
            self.gpu_available_var.set("PyTorch未安装")
        except Exception as e:
            self.gpu_available_var.set(f"GPU检测错误: {str(e)}")
            
    def update_app_status(self):
        """更新应用状态"""
        # 检查数据状态
        if self.main_app.data_manager.has_processed_data():
            self.data_status_var.set("已预处理")
        elif self.main_app.data_manager.has_data():
            self.data_status_var.set("已加载")
        else:
            self.data_status_var.set("未加载")
            
        # 检查模型状态
        if self.main_app.model_manager.is_training:
            self.model_status_var.set("训练中")
            self.app_status_var.set("训练中")
        elif self.main_app.model_manager.is_model_available():
            self.model_status_var.set("已训练")
            self.app_status_var.set("就绪")
        else:
            self.model_status_var.set("未训练")
            self.app_status_var.set("就绪")
            
    def update_runtime(self):
        """更新运行时间"""
        if self.monitoring:
            elapsed = time.time() - self.app_start_time
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)
            seconds = int(elapsed % 60)
            
            runtime_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.runtime_var.set(runtime_str)
            
            # 每秒更新一次
            self.frame.after(1000, self.update_runtime)
            
    def add_log(self, message):
        """添加日志消息（线程安全）"""
        try:
            # 检查是否在主线程中
            if hasattr(self, 'frame') and self.frame.winfo_exists():
                # 如果在主线程中，直接更新
                self._add_log_safe(message)
            else:
                # 如果在其他线程中，使用after方法
                if hasattr(self, 'frame'):
                    self.frame.after(0, lambda: self._add_log_safe(message))
        except Exception as e:
            # 如果GUI还没有准备好，输出到终端
            print(f"[状态面板日志] {message}")

    def _add_log_safe(self, message):
        """安全添加日志（在主线程中执行）"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.insert(tk.END, log_entry)

            if self.auto_scroll_var.get():
                self.log_text.see(tk.END)

            # 限制日志长度
            lines = self.log_text.get(1.0, tk.END).split('\n')
            if len(lines) > 1000:
                # 删除前面的行
                self.log_text.delete(1.0, f"{len(lines)-500}.0")
        except Exception as e:
            print(f"状态面板日志更新失败: {e}")
            
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.add_log("日志已清空")
        
    def save_log(self):
        """保存日志"""
        from tkinter import filedialog
        
        file_path = filedialog.asksaveasfilename(
            title="保存系统日志",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.add_log(f"日志已保存到: {file_path}")
            except Exception as e:
                self.add_log(f"保存日志失败: {str(e)}")
                
    def refresh_status(self):
        """刷新状态"""
        self.add_log("手动刷新系统状态")
        self.update_app_status()
        self.update_gpu_info()
        
    def generate_performance_report(self):
        """生成性能报告"""
        self.add_log("生成性能报告...")
        
        # 收集系统信息
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        report = f"""
系统性能报告
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

CPU使用率: {cpu_percent:.1f}%
内存使用率: {memory.percent:.1f}%
磁盘使用率: {(disk.used / disk.total) * 100:.1f}%

应用运行时间: {self.runtime_var.get()}
数据状态: {self.data_status_var.get()}
模型状态: {self.model_status_var.get()}
"""
        
        # 显示报告
        from tkinter import messagebox
        messagebox.showinfo("性能报告", report)
        
        self.add_log("性能报告生成完成")
        
    def clear_cache(self):
        """清理缓存"""
        self.add_log("开始清理缓存...")
        
        try:
            # 清理可视化缓存
            if hasattr(self.main_app, 'visualization_manager'):
                self.main_app.visualization_manager.clear_cache()
                
            # 清理GPU缓存（如果可用）
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    self.add_log("GPU缓存已清理")
            except:
                pass
                
            self.add_log("缓存清理完成")
            
        except Exception as e:
            self.add_log(f"缓存清理失败: {str(e)}")
            
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def get_frame(self):
        """获取面板框架"""
        return self.frame
