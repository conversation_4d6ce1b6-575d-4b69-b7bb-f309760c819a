import threading
import queue
import time
from typing import Callable, Any, Optional


class ThreadSafeQueue:
    """线程安全队列包装器"""

    def __init__(self, maxsize=0):
        self.queue = queue.Queue(maxsize)

    def put(self, item, block=True, timeout=None):
        """添加项目到队列"""
        return self.queue.put(item, block, timeout)

    def get(self, block=True, timeout=None):
        """从队列获取项目"""
        return self.queue.get(block, timeout)

    def get_nowait(self):
        """非阻塞获取项目"""
        return self.queue.get_nowait()

    def put_nowait(self, item):
        """非阻塞添加项目"""
        return self.queue.put_nowait(item)

    def empty(self):
        """检查队列是否为空"""
        return self.queue.empty()

    def full(self):
        """检查队列是否已满"""
        return self.queue.full()

    def qsize(self):
        """获取队列大小"""
        return self.queue.qsize()

    def task_done(self):
        """标记任务完成"""
        return self.queue.task_done()

    def join(self):
        """等待所有任务完成"""
        return self.queue.join()


class ThreadSafeCallback:
    """线程安全的回调函数包装器"""
    
    def __init__(self, widget, callback):
        self.widget = widget
        self.callback = callback
        
    def __call__(self, *args, **kwargs):
        """调用回调函数"""
        if self.widget and self.widget.winfo_exists():
            self.widget.after(0, lambda: self.callback(*args, **kwargs))


class BackgroundTask:
    """后台任务类"""
    
    def __init__(self, target_func, callback=None, error_callback=None):
        self.target_func = target_func
        self.callback = callback
        self.error_callback = error_callback
        self.thread = None
        self.result = None
        self.error = None
        self.is_running = False
        self.is_cancelled = False
        
    def start(self, *args, **kwargs):
        """启动任务"""
        if self.is_running:
            return False
            
        self.is_running = True
        self.is_cancelled = False
        self.result = None
        self.error = None
        
        def run_task():
            try:
                self.result = self.target_func(*args, **kwargs)
                if self.callback and not self.is_cancelled:
                    self.callback(self.result)
            except Exception as e:
                self.error = e
                if self.error_callback and not self.is_cancelled:
                    self.error_callback(e)
            finally:
                self.is_running = False
                
        self.thread = threading.Thread(target=run_task, daemon=True)
        self.thread.start()
        return True
        
    def cancel(self):
        """取消任务"""
        self.is_cancelled = True
        
    def wait(self, timeout=None):
        """等待任务完成"""
        if self.thread:
            self.thread.join(timeout)
            
    def is_alive(self):
        """检查任务是否还在运行"""
        return self.thread and self.thread.is_alive()


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total_steps=100):
        self.total_steps = total_steps
        self.current_step = 0
        self.callbacks = []
        self.lock = threading.Lock()
        
    def add_callback(self, callback):
        """添加进度回调函数"""
        with self.lock:
            self.callbacks.append(callback)
            
    def remove_callback(self, callback):
        """移除进度回调函数"""
        with self.lock:
            if callback in self.callbacks:
                self.callbacks.remove(callback)
                
    def update(self, step=None, message=""):
        """更新进度"""
        with self.lock:
            if step is not None:
                self.current_step = step
            else:
                self.current_step += 1
                
            progress = min(100, (self.current_step / self.total_steps) * 100)
            
            # 调用所有回调函数
            for callback in self.callbacks:
                try:
                    callback(progress, message)
                except Exception:
                    pass  # 忽略回调函数中的错误
                    
    def reset(self, total_steps=None):
        """重置进度"""
        with self.lock:
            if total_steps is not None:
                self.total_steps = total_steps
            self.current_step = 0
            
    def get_progress(self):
        """获取当前进度百分比"""
        with self.lock:
            return min(100, (self.current_step / self.total_steps) * 100)


class WorkerThread:
    """工作线程类"""
    
    def __init__(self, name="WorkerThread"):
        self.name = name
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.thread = None
        self.running = False
        self.stop_event = threading.Event()
        
    def start(self):
        """启动工作线程"""
        if self.running:
            return
            
        self.running = True
        self.stop_event.clear()
        self.thread = threading.Thread(target=self._worker_loop, 
                                      name=self.name, daemon=True)
        self.thread.start()
        
    def stop(self, timeout=5):
        """停止工作线程"""
        if not self.running:
            return
            
        self.stop_event.set()
        self.running = False
        
        if self.thread:
            self.thread.join(timeout)
            
    def submit_task(self, func, *args, **kwargs):
        """提交任务"""
        if not self.running:
            raise RuntimeError("Worker thread is not running")
            
        task_id = id((func, args, kwargs))
        task = {
            'id': task_id,
            'func': func,
            'args': args,
            'kwargs': kwargs
        }
        
        self.task_queue.put(task)
        return task_id
        
    def get_result(self, timeout=None):
        """获取结果"""
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
            
    def _worker_loop(self):
        """工作线程主循环"""
        while not self.stop_event.is_set():
            try:
                # 获取任务
                task = self.task_queue.get(timeout=0.1)
                
                # 执行任务
                try:
                    result = task['func'](*task['args'], **task['kwargs'])
                    self.result_queue.put({
                        'id': task['id'],
                        'result': result,
                        'error': None
                    })
                except Exception as e:
                    self.result_queue.put({
                        'id': task['id'],
                        'result': None,
                        'error': e
                    })
                    
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception:
                break


class ThreadPool:
    """简单的线程池"""
    
    def __init__(self, max_workers=4):
        self.max_workers = max_workers
        self.workers = []
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.running = False
        
    def start(self):
        """启动线程池"""
        if self.running:
            return
            
        self.running = True
        
        for i in range(self.max_workers):
            worker = WorkerThread(f"PoolWorker-{i}")
            worker.start()
            self.workers.append(worker)
            
    def stop(self, timeout=10):
        """停止线程池"""
        if not self.running:
            return
            
        self.running = False
        
        for worker in self.workers:
            worker.stop(timeout / len(self.workers))
            
        self.workers.clear()
        
    def submit(self, func, *args, **kwargs):
        """提交任务到线程池"""
        if not self.running:
            raise RuntimeError("Thread pool is not running")
            
        # 选择负载最小的工作线程
        worker = min(self.workers, key=lambda w: w.task_queue.qsize())
        return worker.submit_task(func, *args, **kwargs)


class Timer:
    """定时器类"""
    
    def __init__(self, interval, callback, repeat=False):
        self.interval = interval
        self.callback = callback
        self.repeat = repeat
        self.timer = None
        self.running = False
        
    def start(self):
        """启动定时器"""
        if self.running:
            return
            
        self.running = True
        self._schedule_next()
        
    def stop(self):
        """停止定时器"""
        self.running = False
        if self.timer:
            self.timer.cancel()
            self.timer = None
            
    def _schedule_next(self):
        """安排下次执行"""
        if not self.running:
            return
            
        def run_callback():
            if self.running:
                try:
                    self.callback()
                except Exception:
                    pass  # 忽略回调函数中的错误
                    
                if self.repeat and self.running:
                    self._schedule_next()
                else:
                    self.running = False
                    
        self.timer = threading.Timer(self.interval, run_callback)
        self.timer.start()


class EventBus:
    """简单的事件总线"""
    
    def __init__(self):
        self.listeners = {}
        self.lock = threading.Lock()
        
    def subscribe(self, event_type, callback):
        """订阅事件"""
        with self.lock:
            if event_type not in self.listeners:
                self.listeners[event_type] = []
            self.listeners[event_type].append(callback)
            
    def unsubscribe(self, event_type, callback):
        """取消订阅"""
        with self.lock:
            if event_type in self.listeners:
                if callback in self.listeners[event_type]:
                    self.listeners[event_type].remove(callback)
                    
    def publish(self, event_type, *args, **kwargs):
        """发布事件 - 增强版实时事件处理"""
        with self.lock:
            listeners = self.listeners.get(event_type, []).copy()
            
        # 异步执行回调，提高响应性
        if listeners:
            def execute_callbacks():
                for callback in listeners:
                    try:
                        # 检查是否是GUI回调，如果是则使用线程安全方式
                        if hasattr(callback, '__self__') and hasattr(callback.__self__, 'frame'):
                            # GUI组件回调，使用after方法确保线程安全
                            if hasattr(callback.__self__.frame, 'after'):
                                callback.__self__.frame.after(0, lambda: callback(*args, **kwargs))
                            else:
                                callback(*args, **kwargs)
                        else:
                            # 普通回调直接执行
                            callback(*args, **kwargs)
                    except Exception as e:
                        print(f"事件回调错误 ({event_type}): {e}")
            
            # 在新线程中执行回调，避免阻塞主线程
            threading.Thread(target=execute_callbacks, daemon=True).start()
    
    def publish_sync(self, event_type, *args, **kwargs):
        """同步发布事件（用于需要立即执行的情况）"""
        with self.lock:
            listeners = self.listeners.get(event_type, []).copy()
            
        # 在锁外同步执行回调
        for callback in listeners:
            try:
                callback(*args, **kwargs)
            except Exception as e:
                print(f"同步事件回调错误 ({event_type}): {e}")


# 全局事件总线实例
global_event_bus = EventBus()


class RealTimeUpdateManager:
    """实时更新管理器 - 统一管理GUI的实时更新"""
    
    def __init__(self):
        self.update_tasks = {}
        self.is_running = False
        self.lock = threading.Lock()
        
    def register_update_task(self, task_id, update_func, interval_ms=1000):
        """注册更新任务"""
        with self.lock:
            self.update_tasks[task_id] = {
                'func': update_func,
                'interval': interval_ms,
                'last_run': 0,
                'enabled': True
            }
    
    def unregister_update_task(self, task_id):
        """注销更新任务"""
        with self.lock:
            if task_id in self.update_tasks:
                del self.update_tasks[task_id]
    
    def enable_task(self, task_id, enabled=True):
        """启用/禁用更新任务"""
        with self.lock:
            if task_id in self.update_tasks:
                self.update_tasks[task_id]['enabled'] = enabled
    
    def start(self):
        """开始实时更新"""
        if not self.is_running:
            self.is_running = True
            self._run_update_loop()
    
    def stop(self):
        """停止实时更新"""
        self.is_running = False
    
    def _run_update_loop(self):
        """运行更新循环"""
        if not self.is_running:
            return
            
        import time
        current_time = time.time() * 1000  # 转换为毫秒
        
        with self.lock:
            tasks_to_run = []
            for task_id, task_info in self.update_tasks.items():
                if (task_info['enabled'] and 
                    current_time - task_info['last_run'] >= task_info['interval']):
                    tasks_to_run.append((task_id, task_info))
                    task_info['last_run'] = current_time
        
        # 执行需要运行的任务
        for task_id, task_info in tasks_to_run:
            try:
                task_info['func']()
            except Exception as e:
                print(f"实时更新任务错误 ({task_id}): {e}")
        
        # 安排下次更新
        if self.is_running:
            # 使用较短的间隔确保实时性
            threading.Timer(0.1, self._run_update_loop).start()


# 全局实时更新管理器实例
global_update_manager = RealTimeUpdateManager()


def run_in_background(func, callback=None, error_callback=None):
    """在后台运行函数"""
    task = BackgroundTask(func, callback, error_callback)
    task.start()
    return task


def debounce(wait_time):
    """防抖装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            def call_func():
                func(*args, **kwargs)
                
            # 取消之前的定时器
            if hasattr(wrapper, '_timer'):
                wrapper._timer.cancel()
                
            # 创建新的定时器
            wrapper._timer = threading.Timer(wait_time, call_func)
            wrapper._timer.start()
            
        return wrapper
    return decorator


def throttle(wait_time):
    """节流装饰器"""
    def decorator(func):
        last_called = [0]
        
        def wrapper(*args, **kwargs):
            now = time.time()
            if now - last_called[0] >= wait_time:
                last_called[0] = now
                return func(*args, **kwargs)
                
        return wrapper
    return decorator


class AsyncResult:
    """异步结果类"""
    
    def __init__(self):
        self.result = None
        self.error = None
        self.completed = False
        self.event = threading.Event()
        
    def set_result(self, result):
        """设置结果"""
        self.result = result
        self.completed = True
        self.event.set()
        
    def set_error(self, error):
        """设置错误"""
        self.error = error
        self.completed = True
        self.event.set()
        
    def get(self, timeout=None):
        """获取结果"""
        if not self.event.wait(timeout):
            raise TimeoutError("Operation timed out")
            
        if self.error:
            raise self.error
            
        return self.result
        
    def is_ready(self):
        """检查是否完成"""
        return self.completed


def create_async_task(func, *args, **kwargs):
    """创建异步任务"""
    result = AsyncResult()
    
    def run_task():
        try:
            value = func(*args, **kwargs)
            result.set_result(value)
        except Exception as e:
            result.set_error(e)
            
    thread = threading.Thread(target=run_task, daemon=True)
    thread.start()
    
    return result
