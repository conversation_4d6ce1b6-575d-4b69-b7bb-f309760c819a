# 电动汽车充电负荷预测系统 GUI

## 项目概述

本项目为电动汽车充电负荷预测系统提供了一个直观的图形用户界面（GUI），将复杂的深度学习预测模型转化为易于使用的可视化操作界面。

## 系统特性

### 🎯 核心功能
- **参数设置**: 直观的参数配置界面，支持模型、训练、数据等各类参数设置
- **数据导入**: 支持CSV格式数据导入，提供数据预览和质量分析
- **网络训练**: 可视化训练过程，实时监控训练进度和损失曲线
- **预测结果**: 展示预测结果对比图表，支持多种性能指标
- **指标显示**: 全面的性能指标展示，包括MSE、RMSE、MAE、R²等
- **状态监控**: 实时系统资源监控，包括CPU、内存、GPU使用情况
- **数据可视化**: 丰富的图表类型，支持时间序列、相关性分析、分布分析等

### 🏗️ 技术架构
- **深度学习模型**: GRU + Transformer 混合架构
- **优化算法**: SSA (Sparrow Search Algorithm) 超参数优化
- **信号处理**: VMD (Variational Mode Decomposition) 技术
- **GUI框架**: tkinter + ttk 现代化界面
- **可视化**: matplotlib + seaborn 集成
- **多线程**: 非阻塞GUI操作
- **配置管理**: JSON格式参数存储

## 安装要求

### 系统要求
- Python 3.7 或更高版本
- Windows/Linux/macOS 操作系统
- 至少 8GB 内存
- 推荐使用 NVIDIA GPU（可选）

### 依赖包
```bash
pip install numpy pandas matplotlib seaborn scikit-learn torch torchvision psutil
```

## 快速开始

### 1. 启动系统
```bash
python run_gui.py
```

### 2. 数据准备
- 准备CSV格式的充电数据文件
- 确保包含以下列：
  - 充电时间
  - A相电压_均值(V)
  - A相电流_均值(A)
  - 降水量(mm)
  - 平均气温(℃)
  - 最低气温(℃)
  - 最高气温(℃)
  - 总有功功率_总和(kW)

### 3. 使用流程
1. **参数设置**: 在参数面板中配置模型和训练参数
2. **数据导入**: 加载训练数据并进行预处理
3. **模型训练**: 启动训练过程并监控进度
4. **结果预测**: 使用训练好的模型进行预测
5. **结果分析**: 查看性能指标和可视化结果

## 界面说明

### 主界面布局
- **菜单栏**: 文件操作、编辑、视图、工具、帮助
- **工具栏**: 常用功能快捷按钮
- **标签页**: 七个主要功能面板
- **状态栏**: 显示当前状态和进度信息

### 功能面板

#### 1. 参数设置面板
- 树形结构参数分类
- 支持不同参数类型（数值、布尔、选择）
- 参数验证和帮助信息
- 配置保存和加载

#### 2. 数据导入面板
- 文件选择和加载
- 数据预览和统计信息
- 数据质量分析
- 预处理选项配置

#### 3. 网络训练面板
- 训练控制（开始/停止/暂停）
- 实时训练进度显示
- 损失曲线可视化
- 训练历史记录

#### 4. 预测结果面板
- 预测执行控制
- 结果对比图表
- 性能指标计算
- 结果导出功能

#### 5. 指标显示面板
- 多种性能指标
- 对比图表和趋势分析
- 雷达图展示
- 指标历史记录

#### 6. 状态监控面板
- 系统资源监控
- 应用状态跟踪
- 日志信息显示
- GPU性能监控

#### 7. 数据可视化面板
- 多种图表类型
- 交互式图表控制
- 图表参数设置
- 图表保存和导出

## 配置文件

### 默认配置 (configs/default_config.json)
系统使用JSON格式的配置文件，包含以下主要部分：
- `model_params`: 模型参数
- `training_params`: 训练参数
- `data_params`: 数据参数
- `ssa_params`: SSA优化参数
- `vmd_params`: VMD分解参数
- `visualization_params`: 可视化参数
- `system_params`: 系统参数
- `gui_params`: GUI参数

## 文件结构

```
├── gui/                    # GUI模块
│   ├── __init__.py
│   ├── main_app.py        # 主应用程序
│   ├── managers/          # 管理器模块
│   │   ├── __init__.py
│   │   ├── data_manager.py
│   │   ├── model_manager.py
│   │   ├── config_manager.py
│   │   └── visualization_manager.py
│   ├── components/        # 界面组件
│   │   ├── __init__.py
│   │   ├── parameter_panel.py
│   │   ├── data_panel.py
│   │   ├── training_panel.py
│   │   ├── prediction_panel.py
│   │   ├── metrics_panel.py
│   │   ├── status_panel.py
│   │   └── visualization_panel.py
│   └── utils/            # 工具模块
│       ├── __init__.py
│       ├── gui_utils.py
│       ├── threading_utils.py
│       └── validation_utils.py
├── configs/              # 配置文件
│   └── default_config.json
├── logs/                 # 日志文件
├── models/               # 模型保存
├── results/              # 结果保存
├── ev_charging_prediction.py  # 核心预测模型
├── charging_data.csv     # 示例数据
├── run_gui.py           # GUI启动脚本
└── README_GUI.md        # 本文档
```

## 使用技巧

### 1. 参数优化
- 使用SSA算法自动优化超参数
- 根据数据特征调整序列长度
- 合理设置训练轮次和早停策略

### 2. 数据处理
- 确保数据质量，处理缺失值和异常值
- 选择合适的归一化方法
- 考虑使用VMD分解提高预测精度

### 3. 模型训练
- 监控训练过程，避免过拟合
- 使用验证集评估模型性能
- 保存最佳模型检查点

### 4. 结果分析
- 对比多种性能指标
- 分析预测误差分布
- 可视化预测结果趋势

## 故障排除

### 常见问题
1. **启动失败**: 检查Python版本和依赖包
2. **数据加载错误**: 确认数据格式和编码
3. **训练中断**: 检查内存和GPU资源
4. **图表显示异常**: 确认matplotlib配置

### 日志文件
- GUI日志: `logs/gui.log`
- 训练日志: `logs/training.log`
- 性能日志: `logs/performance.log`

## 技术支持

如有问题或建议，请查看日志文件或联系开发团队。

## 版本信息

- 版本: 1.0.0
- 更新日期: 2024年
- 兼容性: Python 3.7+

---

**注意**: 本系统基于深度学习技术，首次使用时可能需要下载预训练模型，请确保网络连接正常。
